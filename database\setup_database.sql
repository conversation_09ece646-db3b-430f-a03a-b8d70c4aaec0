-- إعد<PERSON> قاعدة البيانات المحاسبية
-- Accounting Database Setup

-- إنشاء قاعدة البيانات
-- CREATE DATABASE accounting_db;
-- USE accounting_db;

-- جدول العملاء (Customers)
CREATE TABLE customers (
    customer_id INT IDENTITY(1,1) PRIMARY KEY,
    name NVARCHAR(100) NOT NULL,
    email NVARCHAR(100) UNIQUE,
    phone NVARCHAR(20),
    address NVARCHAR(255),
    created_date DATETIME DEFAULT GETDATE(),
    status NVARCHAR(20) DEFAULT 'active'
);

-- جدول المنتجات (Products)
CREATE TABLE products (
    product_id INT IDENTITY(1,1) PRIMARY KEY,
    name NVARCHAR(100) NOT NULL,
    description NVARCHAR(500),
    price DECIMAL(10,2) NOT NULL,
    category NVARCHAR(50),
    stock_quantity INT DEFAULT 0,
    created_date DATETIME DEFAULT GETDATE(),
    status NVARCHAR(20) DEFAULT 'active'
);

-- جدول المبيعات (Sales)
CREATE TABLE sales (
    sale_id INT IDENTITY(1,1) PRIMARY KEY,
    customer_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    sale_date DATETIME DEFAULT GETDATE(),
    salesperson NVARCHAR(100),
    status NVARCHAR(20) DEFAULT 'completed',
    FOREIGN KEY (customer_id) REFERENCES customers(customer_id),
    FOREIGN KEY (product_id) REFERENCES products(product_id)
);

-- جدول الفواتير (Invoices)
CREATE TABLE invoices (
    invoice_id INT IDENTITY(1,1) PRIMARY KEY,
    customer_id INT NOT NULL,
    invoice_number NVARCHAR(50) UNIQUE NOT NULL,
    invoice_date DATETIME DEFAULT GETDATE(),
    due_date DATETIME,
    subtotal DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    status NVARCHAR(20) DEFAULT 'draft',
    FOREIGN KEY (customer_id) REFERENCES customers(customer_id)
);

-- جدول المدفوعات (Payments)
CREATE TABLE payments (
    payment_id INT IDENTITY(1,1) PRIMARY KEY,
    invoice_id INT NOT NULL,
    payment_date DATETIME DEFAULT GETDATE(),
    amount DECIMAL(10,2) NOT NULL,
    payment_method NVARCHAR(50) NOT NULL,
    reference_number NVARCHAR(100),
    status NVARCHAR(20) DEFAULT 'completed',
    FOREIGN KEY (invoice_id) REFERENCES invoices(invoice_id)
);

-- إدراج بيانات تجريبية

-- العملاء
INSERT INTO customers (name, email, phone, address) VALUES
(N'أحمد محمد علي', '<EMAIL>', '01234567890', N'القاهرة، مصر'),
(N'فاطمة حسن', '<EMAIL>', '01234567891', N'الإسكندرية، مصر'),
(N'محمد عبدالله', '<EMAIL>', '01234567892', N'الجيزة، مصر'),
(N'سارة أحمد', '<EMAIL>', '01234567893', N'المنصورة، مصر'),
(N'عمر خالد', '<EMAIL>', '01234567894', N'أسوان، مصر'),
(N'نور الدين', '<EMAIL>', '01234567895', N'الأقصر، مصر'),
(N'ليلى محمود', '<EMAIL>', '01234567896', N'طنطا، مصر'),
(N'يوسف إبراهيم', '<EMAIL>', '01234567897', N'المنيا، مصر');

-- المنتجات
INSERT INTO products (name, description, price, category, stock_quantity) VALUES
(N'لابتوب ديل', N'لابتوب ديل انسبايرون 15 بوصة', 15000.00, N'إلكترونيات', 25),
(N'هاتف سامسونج', N'هاتف سامسونج جالاكسي S23', 12000.00, N'إلكترونيات', 40),
(N'طابعة HP', N'طابعة HP ليزر جت', 3500.00, N'مكتبية', 15),
(N'كيبورد لوجيتك', N'كيبورد لوجيتك لاسلكي', 450.00, N'إكسسوارات', 60),
(N'ماوس لوجيتك', N'ماوس لوجيتك لاسلكي', 250.00, N'إكسسوارات', 80),
(N'شاشة سامسونج', N'شاشة سامسونج 24 بوصة', 4500.00, N'إلكترونيات', 20),
(N'سماعات سوني', N'سماعات سوني لاسلكية', 2200.00, N'إكسسوارات', 35),
(N'كاميرا كانون', N'كاميرا كانون DSLR', 18000.00, N'تصوير', 10);

-- المبيعات
INSERT INTO sales (customer_id, product_id, quantity, unit_price, total_amount, salesperson) VALUES
(1, 1, 1, 15000.00, 15000.00, N'علي محمد'),
(2, 2, 2, 12000.00, 24000.00, N'سعد أحمد'),
(3, 3, 1, 3500.00, 3500.00, N'علي محمد'),
(1, 4, 3, 450.00, 1350.00, N'نادية حسن'),
(4, 5, 2, 250.00, 500.00, N'سعد أحمد'),
(2, 6, 1, 4500.00, 4500.00, N'علي محمد'),
(5, 7, 2, 2200.00, 4400.00, N'نادية حسن'),
(3, 8, 1, 18000.00, 18000.00, N'سعد أحمد'),
(6, 1, 1, 15000.00, 15000.00, N'علي محمد'),
(7, 2, 1, 12000.00, 12000.00, N'نادية حسن'),
(8, 4, 5, 450.00, 2250.00, N'سعد أحمد'),
(4, 5, 3, 250.00, 750.00, N'علي محمد');

-- الفواتير
INSERT INTO invoices (customer_id, invoice_number, subtotal, tax_amount, total_amount, due_date, status) VALUES
(1, 'INV-2024-001', 16350.00, 2452.50, 18802.50, DATEADD(day, 30, GETDATE()), 'sent'),
(2, 'INV-2024-002', 28500.00, 4275.00, 32775.00, DATEADD(day, 30, GETDATE()), 'paid'),
(3, 'INV-2024-003', 21500.00, 3225.00, 24725.00, DATEADD(day, 30, GETDATE()), 'sent'),
(4, 'INV-2024-004', 1250.00, 187.50, 1437.50, DATEADD(day, 30, GETDATE()), 'paid'),
(5, 'INV-2024-005', 4400.00, 660.00, 5060.00, DATEADD(day, 30, GETDATE()), 'draft'),
(6, 'INV-2024-006', 15000.00, 2250.00, 17250.00, DATEADD(day, 30, GETDATE()), 'sent'),
(7, 'INV-2024-007', 12000.00, 1800.00, 13800.00, DATEADD(day, 30, GETDATE()), 'paid'),
(8, 'INV-2024-008', 2250.00, 337.50, 2587.50, DATEADD(day, 30, GETDATE()), 'sent');

-- المدفوعات
INSERT INTO payments (invoice_id, amount, payment_method, reference_number) VALUES
(2, 32775.00, N'تحويل بنكي', 'TXN-2024-001'),
(4, 1437.50, N'نقدي', 'CASH-2024-001'),
(7, 13800.00, N'بطاقة ائتمان', 'CARD-2024-001'),
(2, 0.00, N'خصم', 'DISC-2024-001'); -- دفعة جزئية أو خصم

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IX_sales_customer_id ON sales(customer_id);
CREATE INDEX IX_sales_product_id ON sales(product_id);
CREATE INDEX IX_sales_sale_date ON sales(sale_date);
CREATE INDEX IX_invoices_customer_id ON invoices(customer_id);
CREATE INDEX IX_invoices_status ON invoices(status);
CREATE INDEX IX_payments_invoice_id ON payments(invoice_id);

-- إنشاء views مفيدة للتقارير
CREATE VIEW vw_customer_sales AS
SELECT 
    c.customer_id,
    c.name as customer_name,
    COUNT(s.sale_id) as total_orders,
    SUM(s.total_amount) as total_sales,
    AVG(s.total_amount) as avg_order_value,
    MAX(s.sale_date) as last_purchase_date
FROM customers c
LEFT JOIN sales s ON c.customer_id = s.customer_id
GROUP BY c.customer_id, c.name;

CREATE VIEW vw_product_performance AS
SELECT 
    p.product_id,
    p.name as product_name,
    p.category,
    p.price,
    COALESCE(SUM(s.quantity), 0) as total_sold,
    COALESCE(SUM(s.total_amount), 0) as total_revenue,
    p.stock_quantity as current_stock
FROM products p
LEFT JOIN sales s ON p.product_id = s.product_id
GROUP BY p.product_id, p.name, p.category, p.price, p.stock_quantity;

CREATE VIEW vw_monthly_sales AS
SELECT 
    YEAR(sale_date) as sale_year,
    MONTH(sale_date) as sale_month,
    COUNT(*) as total_transactions,
    SUM(total_amount) as total_revenue,
    AVG(total_amount) as avg_transaction_value
FROM sales
GROUP BY YEAR(sale_date), MONTH(sale_date);

-- إضافة بعض البيانات للأشهر السابقة
INSERT INTO sales (customer_id, product_id, quantity, unit_price, total_amount, sale_date, salesperson) VALUES
(1, 2, 1, 12000.00, 12000.00, DATEADD(month, -1, GETDATE()), N'علي محمد'),
(3, 1, 1, 15000.00, 15000.00, DATEADD(month, -1, GETDATE()), N'سعد أحمد'),
(5, 6, 1, 4500.00, 4500.00, DATEADD(month, -2, GETDATE()), N'نادية حسن'),
(2, 8, 1, 18000.00, 18000.00, DATEADD(month, -2, GETDATE()), N'علي محمد'),
(4, 3, 2, 3500.00, 7000.00, DATEADD(month, -3, GETDATE()), N'سعد أحمد');
