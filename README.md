# نظام الوكيل الذكي للاستعلامات المحاسبية

## 🎯 نظرة عامة
نظام وكيل ذكي للإجابة عن استعلامات النظام المحاسبي بلغة طبيعية وتحليل البيانات بشكل فوري.

## ✨ المميزات
- **استعلامات بلغة طبيعية**: اكتب أسئلتك بالعربية أو الإنجليزية
- **تحليل ذكي**: تحويل الأسئلة إلى استعلامات SQL تلقائياً
- **تصور البيانات**: رسوم بيانية تفاعلية للنتائج
- **واجهة سهلة**: تصميم بسيط وسهل الاستخدام
- **دعم متعدد اللغات**: عربي وإنجليزي

## 🛠️ التقنيات المستخدمة
- **Backend**: FastAPI
- **Frontend**: Django
- **Database**: SQL Server
- **LLM**: Groq (qwen3-32b)
- **Data Visualization**: Plotly, Matplotlib
- **Session Management**: SQLite

## 📋 متطلبات التشغيل
- Python 3.8+
- SQL Server
- Groq API Key

## 🚀 التثبيت والتشغيل

### 1. تحضير البيئة
```bash
# إنشاء بيئة افتراضية
python -m venv venv

# تفعيل البيئة الافتراضية
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# تثبيت المتطلبات
pip install -r requirements.txt
```

### 2. إعداد متغيرات البيئة
```bash
# نسخ ملف البيئة
cp .env.example .env

# تحرير الملف وإضافة البيانات الصحيحة:
# - GROQ_API_KEY: مفتاح API من Groq
# - DB_SERVER: عنوان خادم SQL Server
# - DB_NAME: اسم قاعدة البيانات
# - DB_USERNAME: اسم المستخدم
# - DB_PASSWORD: كلمة المرور
```

### 3. إعداد قاعدة البيانات
```bash
# إعداد قاعدة البيانات مع البيانات التجريبية
python setup_db.py
```

### 4. اختبار النظام
```bash
# اختبار سريع للنظام
python test_system.py
```

### 5. تشغيل النظام الكامل
```bash
# تشغيل النظام الكامل (Backend + Frontend)
python run_system.py

# أو تشغيل كل خدمة منفصلة:

# Backend API
cd backend
uvicorn main:app --reload --host 127.0.0.1 --port 8000

# Frontend (في terminal آخر)
cd frontend
python manage.py runserver 127.0.0.1:8080
```

## 📁 هيكل المشروع
```
agentpy/
├── backend/                 # FastAPI Backend
│   ├── main.py             # نقطة دخول API
│   ├── models/             # نماذج البيانات
│   ├── services/           # خدمات العمل
│   └── utils/              # أدوات مساعدة
├── frontend/               # Django Frontend
│   ├── manage.py
│   ├── templates/          # قوالب HTML
│   └── static/             # ملفات CSS/JS
├── database/               # إعدادات قاعدة البيانات
├── tests/                  # الاختبارات
├── logs/                   # ملفات السجلات
└── docs/                   # الوثائق
```

## 🔧 الاستخدام
1. افتح المتصفح على `http://127.0.0.1:8080`
2. اكتب سؤالك بلغة طبيعية مثل:
   - "ما هو أكثر منتج مبيعاً هذا الأسبوع؟"
   - "ما إجمالي المبيعات الشهر الماضي؟"
   - "من هو العميل الأكثر شراءً؟"
   - "What are the top selling products?"
   - "Show me monthly sales trends"
3. شاهد النتائج مع الرسوم البيانية التفاعلية

## 🔍 الروابط المهمة
- **الواجهة الرئيسية**: http://127.0.0.1:8080
- **API Documentation**: http://127.0.0.1:8000/docs
- **API Health Check**: http://127.0.0.1:8000/health
- **Django Admin**: http://127.0.0.1:8080/admin

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. خطأ في الاتصال بقاعدة البيانات
```bash
# تأكد من تشغيل SQL Server
# تحقق من بيانات الاتصال في ملف .env
# اختبر الاتصال:
python -c "from backend.utils.database_utils import DatabaseUtils; print(DatabaseUtils.test_connection())"
```

#### 2. خطأ في مفتاح Groq API
```bash
# تأكد من صحة GROQ_API_KEY في ملف .env
# احصل على مفتاح من: https://console.groq.com/
```

#### 3. خطأ في تثبيت المتطلبات
```bash
# تحديث pip
python -m pip install --upgrade pip

# إعادة تثبيت المتطلبات
pip install -r requirements.txt --force-reinstall
```

#### 4. مشكلة في المنافذ (Ports)
```bash
# تغيير منافذ التشغيل إذا كانت مستخدمة:
# Backend: تعديل FASTAPI_PORT في .env
# Frontend: python manage.py runserver 127.0.0.1:8081
```

### سجلات الأخطاء:
- Backend logs: `logs/app.log`
- Frontend logs: `frontend/logs/django.log`

## 🤝 المساهمة
نرحب بمساهماتكم! يرجى قراءة دليل المساهمة قبل البدء.

## 📄 الترخيص
هذا المشروع مرخص تحت رخصة MIT.
