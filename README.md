# نظام الوكيل الذكي للاستعلامات المحاسبية

## 🎯 نظرة عامة
نظام وكيل ذكي للإجابة عن استعلامات النظام المحاسبي بلغة طبيعية وتحليل البيانات بشكل فوري.

## ✨ المميزات
- **استعلامات بلغة طبيعية**: اكتب أسئلتك بالعربية أو الإنجليزية
- **تحليل ذكي**: تحويل الأسئلة إلى استعلامات SQL تلقائياً
- **تصور البيانات**: رسوم بيانية تفاعلية للنتائج
- **واجهة سهلة**: تصميم بسيط وسهل الاستخدام
- **دعم متعدد اللغات**: عربي وإنجليزي

## 🛠️ التقنيات المستخدمة
- **Backend**: FastAPI
- **Frontend**: Django
- **Database**: SQL Server
- **LLM**: Groq (qwen3-32b)
- **Data Visualization**: Plotly, Matplotlib
- **Session Management**: SQLite

## 📋 متطلبات التشغيل
- Python 3.8+
- SQL Server
- Groq API Key

## 🚀 التثبيت والتشغيل

### 1. تحضير البيئة
```bash
# إنشاء بيئة افتراضية
python -m venv venv

# تفعيل البيئة الافتراضية
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# تثبيت المتطلبات
pip install -r requirements.txt
```

### 2. إعداد متغيرات البيئة
```bash
# نسخ ملف البيئة
cp .env.example .env

# تحرير الملف وإضافة البيانات الصحيحة
```

### 3. تشغيل النظام
```bash
# تشغيل Backend API
cd backend
uvicorn main:app --reload --host 127.0.0.1 --port 8000

# تشغيل Frontend (في terminal آخر)
cd frontend
python manage.py runserver
```

## 📁 هيكل المشروع
```
agentpy/
├── backend/                 # FastAPI Backend
│   ├── main.py             # نقطة دخول API
│   ├── models/             # نماذج البيانات
│   ├── services/           # خدمات العمل
│   └── utils/              # أدوات مساعدة
├── frontend/               # Django Frontend
│   ├── manage.py
│   ├── templates/          # قوالب HTML
│   └── static/             # ملفات CSS/JS
├── database/               # إعدادات قاعدة البيانات
├── tests/                  # الاختبارات
├── logs/                   # ملفات السجلات
└── docs/                   # الوثائق
```

## 🔧 الاستخدام
1. افتح المتصفح على `http://localhost:8000`
2. اكتب سؤالك بلغة طبيعية مثل:
   - "ما هو أكثر منتج مبيعاً هذا الأسبوع؟"
   - "ما إجمالي المبيعات الشهر الماضي؟"
   - "من هو العميل الأكثر شراءً؟"
3. شاهد النتائج مع الرسوم البيانية التفاعلية

## 🤝 المساهمة
نرحب بمساهماتكم! يرجى قراءة دليل المساهمة قبل البدء.

## 📄 الترخيص
هذا المشروع مرخص تحت رخصة MIT.
