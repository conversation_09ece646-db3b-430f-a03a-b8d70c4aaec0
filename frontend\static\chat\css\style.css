/* Custom styles for the accounting agent chat */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
}

/* Chat Messages */
.chat-message {
    margin-bottom: 1rem;
    animation: fadeInUp 0.3s ease-out;
}

.chat-message.user {
    text-align: left;
}

.chat-message.bot {
    text-align: right;
}

.message-bubble {
    display: inline-block;
    max-width: 80%;
    padding: 0.75rem 1rem;
    border-radius: 1rem;
    word-wrap: break-word;
}

.message-bubble.user {
    background-color: var(--primary-color);
    color: white;
    border-bottom-left-radius: 0.25rem;
}

.message-bubble.bot {
    background-color: white;
    color: var(--dark-color);
    border: 1px solid #dee2e6;
    border-bottom-right-radius: 0.25rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.message-time {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* SQL Query Display */
.sql-query {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 1rem 0;
    font-family: 'Courier New', monospace;
    position: relative;
}

.sql-query .copy-btn {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    opacity: 0.7;
}

.sql-query .copy-btn:hover {
    opacity: 1;
}

.sql-explanation {
    background-color: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 0.5rem;
    padding: 0.75rem;
    margin: 0.5rem 0;
}

/* Chart Container */
.chart-container {
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 1rem 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Data Table */
.data-table {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
}

.data-table table {
    margin-bottom: 0;
}

.data-table th {
    background-color: var(--primary-color);
    color: white;
    position: sticky;
    top: 0;
    z-index: 10;
}

/* Confidence Badge */
.confidence-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: bold;
}

.confidence-high {
    background-color: var(--success-color);
    color: white;
}

.confidence-medium {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.confidence-low {
    background-color: var(--danger-color);
    color: white;
}

/* Loading Animation */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background-color: white;
    border-radius: 1rem;
    border-bottom-right-radius: 0.25rem;
    border: 1px solid #dee2e6;
    margin-bottom: 1rem;
}

.typing-dots {
    display: flex;
    gap: 0.25rem;
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--secondary-color);
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(13, 110, 253, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0);
    }
}

/* Input Focus */
#query-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Send Button Animation */
#send-btn:hover {
    animation: pulse 1s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .message-bubble {
        max-width: 95%;
    }
    
    .chart-container {
        padding: 0.5rem;
    }
    
    .sql-query {
        padding: 0.5rem;
        font-size: 0.875rem;
    }
}

/* Custom Scrollbar */
#chat-messages::-webkit-scrollbar {
    width: 6px;
}

#chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#chat-messages::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: 3px;
}

#chat-messages::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Status Indicator */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

/* Error Message */
.error-message {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 0.75rem;
    border-radius: 0.5rem;
    margin: 0.5rem 0;
}

/* Success Message */
.success-message {
    background-color: #d1edff;
    border: 1px solid #b3d9ff;
    color: #0c5460;
    padding: 0.75rem;
    border-radius: 0.5rem;
    margin: 0.5rem 0;
}
