"""
Analysis service for data insights and visualization
"""
import logging
from typing import List, Dict, Any, Optional
import pandas as pd
from backend.models.schemas import QueryResult, AnalysisResult
from backend.utils.chart_generator import ChartGenerator

logger = logging.getLogger(__name__)

class AnalysisService:
    """Service for analyzing query results and generating insights"""

    def __init__(self):
        self.chart_generator = ChartGenerator()
        self.chart_types = {
            'bar': self._create_bar_chart,
            'line': self._create_line_chart,
            'pie': self._create_pie_chart,
            'scatter': self._create_scatter_chart,
            'table': self._create_table_chart
        }
    
    def analyze_results(
        self, 
        query_result: QueryResult, 
        original_query: str, 
        language: str = "ar"
    ) -> AnalysisResult:
        """
        Analyze query results and generate insights
        
        Args:
            query_result: Results from database query
            original_query: Original user query
            language: Language preference
            
        Returns:
            AnalysisResult with summary, insights, and chart data
        """
        try:
            if query_result.row_count == 0:
                return self._create_empty_analysis(language)
            
            # Convert to DataFrame for analysis
            df = pd.DataFrame(query_result.data)
            
            # Generate summary
            summary = self._generate_summary(df, query_result, language)
            
            # Generate insights
            insights = self._generate_insights(df, original_query, language)
            
            # Determine best chart type and create chart data
            chart_type = self._determine_chart_type(df, original_query)
            chart_data = self._create_chart_data(df, chart_type, language)
            
            return AnalysisResult(
                summary=summary,
                insights=insights,
                chart_data=chart_data,
                chart_type=chart_type
            )
            
        except Exception as e:
            logger.error(f"Analysis failed: {str(e)}")
            return self._create_error_analysis(str(e), language)
    
    def _generate_summary(
        self, 
        df: pd.DataFrame, 
        query_result: QueryResult, 
        language: str
    ) -> str:
        """Generate summary of the results"""
        if language == "ar":
            return f"تم العثور على {query_result.row_count} نتيجة في {query_result.execution_time:.2f} ثانية. البيانات تحتوي على {len(df.columns)} عمود."
        else:
            return f"Found {query_result.row_count} results in {query_result.execution_time:.2f} seconds. Data contains {len(df.columns)} columns."
    
    def _generate_insights(
        self, 
        df: pd.DataFrame, 
        original_query: str, 
        language: str
    ) -> List[str]:
        """Generate insights from the data"""
        insights = []
        
        try:
            # Numeric columns analysis
            numeric_cols = df.select_dtypes(include=['number']).columns
            
            if len(numeric_cols) > 0:
                for col in numeric_cols:
                    if not df[col].empty:
                        max_val = df[col].max()
                        min_val = df[col].min()
                        avg_val = df[col].mean()
                        
                        if language == "ar":
                            insights.append(f"أعلى قيمة في {col}: {max_val:,.2f}")
                            insights.append(f"أقل قيمة في {col}: {min_val:,.2f}")
                            insights.append(f"المتوسط في {col}: {avg_val:,.2f}")
                        else:
                            insights.append(f"Highest {col}: {max_val:,.2f}")
                            insights.append(f"Lowest {col}: {min_val:,.2f}")
                            insights.append(f"Average {col}: {avg_val:,.2f}")
            
            # Categorical analysis
            categorical_cols = df.select_dtypes(include=['object']).columns
            
            for col in categorical_cols:
                if not df[col].empty:
                    unique_count = df[col].nunique()
                    most_common = df[col].mode().iloc[0] if not df[col].mode().empty else "N/A"
                    
                    if language == "ar":
                        insights.append(f"عدد القيم المختلفة في {col}: {unique_count}")
                        insights.append(f"القيمة الأكثر تكراراً في {col}: {most_common}")
                    else:
                        insights.append(f"Unique values in {col}: {unique_count}")
                        insights.append(f"Most common {col}: {most_common}")
            
            # Limit insights to avoid overwhelming
            return insights[:6]
            
        except Exception as e:
            logger.error(f"Error generating insights: {str(e)}")
            if language == "ar":
                return ["تعذر تحليل البيانات بشكل تفصيلي"]
            else:
                return ["Unable to perform detailed analysis"]
    
    def _determine_chart_type(self, df: pd.DataFrame, query: str) -> str:
        """Determine the best chart type for the data"""
        try:
            # Simple heuristics for chart type selection
            numeric_cols = len(df.select_dtypes(include=['number']).columns)
            categorical_cols = len(df.select_dtypes(include=['object']).columns)
            row_count = len(df)
            
            # Keywords in query to help determine chart type
            query_lower = query.lower()
            
            if any(word in query_lower for word in ['trend', 'over time', 'timeline', 'اتجاه', 'مع الوقت']):
                return 'line'
            elif any(word in query_lower for word in ['distribution', 'share', 'percentage', 'توزيع', 'نسبة']):
                return 'pie'
            elif numeric_cols >= 2:
                return 'scatter'
            elif numeric_cols >= 1 and categorical_cols >= 1:
                return 'bar'
            elif row_count <= 20:
                return 'table'
            else:
                return 'bar'
                
        except Exception:
            return 'table'
    
    def _create_chart_data(self, df: pd.DataFrame, chart_type: str, language: str = "ar") -> Optional[Dict[str, Any]]:
        """Create chart data based on chart type"""
        try:
            if chart_type in self.chart_types:
                return self.chart_types[chart_type](df, language)
            else:
                return self._create_table_chart(df, language)
        except Exception as e:
            logger.error(f"Error creating chart data: {str(e)}")
            return None
    
    def _create_bar_chart(self, df: pd.DataFrame, language: str = "ar") -> Dict[str, Any]:
        """Create bar chart data"""
        numeric_cols = df.select_dtypes(include=['number']).columns
        categorical_cols = df.select_dtypes(include=['object']).columns

        if len(numeric_cols) > 0 and len(categorical_cols) > 0:
            x_col = categorical_cols[0]
            y_col = numeric_cols[0]

            # Group by categorical column and sum numeric values
            grouped = df.groupby(x_col)[y_col].sum().reset_index()

            title = f'{y_col} حسب {x_col}' if language == "ar" else f'{y_col} by {x_col}'

            return self.chart_generator.create_bar_chart(
                data=grouped.to_dict('records'),
                x_col=x_col,
                y_col=y_col,
                title=title,
                language=language
            )

        return self._create_table_chart(df, language)
    
    def _create_line_chart(self, df: pd.DataFrame, language: str = "ar") -> Dict[str, Any]:
        """Create line chart data"""
        numeric_cols = df.select_dtypes(include=['number']).columns

        if len(numeric_cols) >= 1:
            x_col = df.columns[0]  # Assume first column is x-axis
            y_col = numeric_cols[0]

            title = f'{y_col} عبر {x_col}' if language == "ar" else f'{y_col} over {x_col}'

            return self.chart_generator.create_line_chart(
                data=df.to_dict('records'),
                x_col=x_col,
                y_col=y_col,
                title=title,
                language=language
            )

        return self._create_table_chart(df, language)
    
    def _create_pie_chart(self, df: pd.DataFrame, language: str = "ar") -> Dict[str, Any]:
        """Create pie chart data"""
        categorical_cols = df.select_dtypes(include=['object']).columns
        numeric_cols = df.select_dtypes(include=['number']).columns

        if len(categorical_cols) > 0:
            label_col = categorical_cols[0]
            value_col = numeric_cols[0] if len(numeric_cols) > 0 else None

            if value_col:
                grouped = df.groupby(label_col)[value_col].sum().reset_index()
                title = f'توزيع {value_col} حسب {label_col}' if language == "ar" else f'Distribution of {value_col} by {label_col}'

                return self.chart_generator.create_pie_chart(
                    data=grouped.to_dict('records'),
                    labels_col=label_col,
                    values_col=value_col,
                    title=title,
                    language=language
                )
            else:
                # Count occurrences
                counts = df[label_col].value_counts().reset_index()
                counts.columns = [label_col, 'count']
                title = f'توزيع {label_col}' if language == "ar" else f'Distribution of {label_col}'

                return self.chart_generator.create_pie_chart(
                    data=counts.to_dict('records'),
                    labels_col=label_col,
                    values_col='count',
                    title=title,
                    language=language
                )

        return self._create_table_chart(df, language)
    
    def _create_scatter_chart(self, df: pd.DataFrame, language: str = "ar") -> Dict[str, Any]:
        """Create scatter chart data"""
        numeric_cols = df.select_dtypes(include=['number']).columns

        if len(numeric_cols) >= 2:
            x_col = numeric_cols[0]
            y_col = numeric_cols[1]

            title = f'{y_col} مقابل {x_col}' if language == "ar" else f'{y_col} vs {x_col}'

            return self.chart_generator.create_scatter_chart(
                data=df.to_dict('records'),
                x_col=x_col,
                y_col=y_col,
                title=title,
                language=language
            )

        return self._create_table_chart(df, language)

    def _create_table_chart(self, df: pd.DataFrame, language: str = "ar") -> Dict[str, Any]:
        """Create table data"""
        title = 'جدول البيانات' if language == "ar" else 'Data Table'

        return self.chart_generator.create_table_chart(
            data=df.to_dict('records'),
            title=title,
            language=language
        )
    
    def _create_empty_analysis(self, language: str) -> AnalysisResult:
        """Create analysis for empty results"""
        if language == "ar":
            return AnalysisResult(
                summary="لم يتم العثور على نتائج",
                insights=["لا توجد بيانات للتحليل"],
                chart_data=None,
                chart_type="table"
            )
        else:
            return AnalysisResult(
                summary="No results found",
                insights=["No data available for analysis"],
                chart_data=None,
                chart_type="table"
            )
    
    def _create_error_analysis(self, error: str, language: str) -> AnalysisResult:
        """Create analysis for errors"""
        if language == "ar":
            return AnalysisResult(
                summary=f"خطأ في التحليل: {error}",
                insights=["تعذر تحليل البيانات"],
                chart_data=None,
                chart_type="table"
            )
        else:
            return AnalysisResult(
                summary=f"Analysis error: {error}",
                insights=["Unable to analyze data"],
                chart_data=None,
                chart_type="table"
            )
