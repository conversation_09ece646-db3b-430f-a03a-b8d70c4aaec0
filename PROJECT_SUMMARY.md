# 🤖 نظام الوكيل الذكي للاستعلامات المحاسبية
## Intelligent Accounting Agent System

### 📋 ملخص المشروع

تم تطوير نظام متكامل للوكيل الذكي يتيح للمستخدمين إجراء استعلامات محاسبية بلغة طبيعية (عربي/إنجليزي) والحصول على نتائج تحليلية مع رسوم بيانية تفاعلية.

### 🏗️ المعمارية التقنية

#### Backend (FastAPI)
- **Framework**: FastAPI مع Python 3.8+
- **LLM Integration**: Groq API مع نموذج qwen3-32b
- **LangChain**: لإدارة التفاعل مع النماذج اللغوية
- **Database**: SQL Server مع pyodbc
- **Analysis**: Pandas + Plotly للتحليل والتصور

#### Frontend (Django)
- **Framework**: Django 4.2+ مع واجهة عربية
- **UI**: Bootstrap 5 مع دعم RTL
- **Charts**: Plotly.js للرسوم التفاعلية
- **Real-time**: AJAX للتفاعل المباشر

#### Database Schema
```sql
- customers (العملاء): معلومات العملاء
- products (المنتجات): كتالوج المنتجات  
- sales (المبيعات): معاملات البيع
- invoices (الفواتير): سجلات الفواتير
- payments (المدفوعات): سجلات المدفوعات
```

### 🚀 المميزات المطورة

#### 1. معالجة اللغة الطبيعية
- ✅ دعم اللغة العربية والإنجليزية
- ✅ تحويل الاستعلامات الطبيعية إلى SQL
- ✅ شرح الاستعلامات المولدة
- ✅ تقدير مستوى الثقة في النتائج

#### 2. تحليل البيانات والتصور
- ✅ رسوم بيانية تفاعلية (Bar, Line, Pie, Scatter)
- ✅ جداول بيانات قابلة للتصفح
- ✅ تحليل ذكي للنتائج
- ✅ رؤى تلقائية من البيانات

#### 3. إدارة الجلسات
- ✅ حفظ تاريخ المحادثات
- ✅ استرجاع الجلسات السابقة
- ✅ إحصائيات الاستخدام
- ✅ قاعدة بيانات SQLite للجلسات

#### 4. تحسين الأداء
- ✅ تخزين مؤقت للاستعلامات
- ✅ تخزين مؤقت لاستجابات LLM
- ✅ مراقبة الأداء في الوقت الفعلي
- ✅ تحسين استعلامات SQL

#### 5. واجهة المستخدم
- ✅ تصميم عربي متجاوب
- ✅ أمثلة سريعة للاستعلامات
- ✅ مؤشرات حالة النظام
- ✅ نظام مساعدة تفاعلي

### 📁 هيكل المشروع

```
agentpy/
├── backend/                 # FastAPI Backend
│   ├── main.py             # نقطة دخول API
│   ├── config.py           # إعدادات النظام
│   ├── models/             # نماذج البيانات
│   │   └── schemas.py      # Pydantic schemas
│   ├── services/           # خدمات العمل
│   │   ├── llm_service.py      # خدمة LLM
│   │   ├── database_service.py # خدمة قاعدة البيانات
│   │   ├── analysis_service.py # خدمة التحليل
│   │   └── session_service.py  # خدمة الجلسات
│   └── utils/              # أدوات مساعدة
│       ├── langchain_config.py    # تكوين LangChain
│       ├── chart_generator.py     # مولد الرسوم
│       ├── database_utils.py      # أدوات قاعدة البيانات
│       └── performance_optimizer.py # تحسين الأداء
├── frontend/               # Django Frontend
│   ├── manage.py
│   ├── accounting_frontend/ # إعدادات Django
│   ├── chat/               # تطبيق المحادثة
│   ├── templates/          # قوالب HTML
│   └── static/             # ملفات CSS/JS
├── database/               # إعدادات قاعدة البيانات
│   └── setup_database.sql # سكريبت الإعداد
├── tests/                  # الاختبارات
│   └── test_system.py      # اختبارات النظام
├── requirements.txt        # متطلبات Python
├── .env.example           # مثال متغيرات البيئة
├── setup_db.py           # إعداد قاعدة البيانات
├── run_system.py          # تشغيل النظام البسيط
├── start_system.py        # تشغيل النظام المحسن
└── test_system.py         # اختبار سريع
```

### 🔧 ملفات التشغيل

#### 1. الإعداد الأولي
```bash
python setup_db.py          # إعداد قاعدة البيانات
```

#### 2. الاختبار السريع
```bash
python test_system.py       # اختبار مكونات النظام
```

#### 3. التشغيل
```bash
python start_system.py      # تشغيل محسن مع مراقبة
python run_system.py        # تشغيل بسيط
```

### 📊 API Endpoints

#### Core Endpoints
- `GET /` - الصفحة الرئيسية
- `GET /health` - فحص صحة النظام
- `POST /query` - معالجة الاستعلامات
- `GET /sessions/{id}` - معلومات الجلسة
- `GET /sessions/{id}/history` - تاريخ الجلسة

#### Performance Endpoints
- `GET /performance/metrics` - مقاييس الأداء
- `POST /performance/clear-cache` - مسح التخزين المؤقت

### 🧪 الاختبارات

#### اختبارات تلقائية
- اختبار الاتصال بقاعدة البيانات
- اختبار خدمة LLM
- اختبار خدمة الجلسات
- اختبار API endpoints
- اختبار استعلامات عينة

#### اختبارات يدوية
- واجهة المستخدم
- الاستجابة التفاعلية
- دقة النتائج
- أداء النظام

### 🔒 الأمان والموثوقية

#### تدابير الأمان
- تنظيف استعلامات SQL
- منع العمليات الخطيرة (DROP, DELETE)
- التحقق من صحة المدخلات
- إدارة الأخطاء الشاملة

#### الموثوقية
- إعادة المحاولة عند الفشل
- تسجيل شامل للأحداث
- مراقبة الأداء
- تخزين مؤقت ذكي

### 📈 مقاييس الأداء

#### مقاييس مراقبة
- عدد الاستعلامات الإجمالي
- معدل النجاح
- متوسط وقت الاستجابة
- معدل إصابة التخزين المؤقت
- استخدام الذاكرة

#### تحسينات الأداء
- تخزين مؤقت للاستعلامات المتكررة
- تحسين استعلامات SQL
- معالجة متوازية
- ضغط البيانات

### 🌟 نقاط القوة

1. **سهولة الاستخدام**: واجهة بديهية باللغة العربية
2. **ذكاء اصطناعي متقدم**: استخدام أحدث نماذج LLM
3. **تحليل شامل**: رؤى ذكية ورسوم تفاعلية
4. **أداء محسن**: تخزين مؤقت ومراقبة
5. **مرونة عالية**: دعم متعدد اللغات
6. **موثوقية**: إدارة أخطاء شاملة

### 🚀 إمكانيات التطوير المستقبلي

1. **تحسينات LLM**:
   - دعم نماذج إضافية
   - تدريب مخصص للمجال المحاسبي
   - تحسين دقة الاستعلامات

2. **ميزات إضافية**:
   - تصدير التقارير (PDF, Excel)
   - جدولة التقارير التلقائية
   - تنبيهات ذكية

3. **تحسينات الأداء**:
   - توزيع الحمولة
   - قواعد بيانات متعددة
   - تخزين مؤقت موزع

4. **الأمان**:
   - مصادقة المستخدمين
   - تشفير البيانات
   - تدقيق العمليات

### 📞 الدعم والصيانة

- **التوثيق**: شامل ومفصل
- **الاختبارات**: تغطية شاملة
- **المراقبة**: في الوقت الفعلي
- **السجلات**: تفصيلية ومنظمة

---

**تم تطوير هذا النظام بعناية فائقة ليكون حلاً متكاملاً وموثوقاً للاستعلامات المحاسبية الذكية.**
