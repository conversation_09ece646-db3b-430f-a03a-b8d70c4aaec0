#!/usr/bin/env python3
"""
Comprehensive system tests for the Accounting Agent System
"""
import os
import sys
import pytest
import requests
import time
import json
from pathlib import Path

# Add backend to path
sys.path.append(str(Path(__file__).parent.parent / "backend"))

from backend.config import settings
from backend.utils.database_utils import DatabaseUtils
from backend.services.llm_service import LLMService
from backend.services.session_service import SessionService

class TestSystemIntegration:
    """Integration tests for the complete system"""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup test environment"""
        self.backend_url = "http://127.0.0.1:8000"
        self.frontend_url = "http://127.0.0.1:8080"
        self.test_session_id = "test-session-123"
        
    def test_database_connection(self):
        """Test database connectivity"""
        print("🔍 Testing database connection...")
        result = DatabaseUtils.test_connection()
        assert result, "Database connection failed"
        print("✅ Database connection successful")
    
    def test_llm_service(self):
        """Test LLM service functionality"""
        print("🤖 Testing LLM service...")
        
        try:
            llm_service = LLMService()
            
            # Test Arabic query
            result = llm_service.generate_sql_query("ما هو أكثر منتج مبيعاً؟", "ar")
            assert result.sql, "SQL query should not be empty"
            assert result.explanation, "Explanation should not be empty"
            assert 0 <= result.confidence <= 1, "Confidence should be between 0 and 1"
            print(f"✅ Arabic query test passed - Confidence: {result.confidence}")
            
            # Test English query
            result = llm_service.generate_sql_query("What are the top selling products?", "en")
            assert result.sql, "SQL query should not be empty"
            assert result.explanation, "Explanation should not be empty"
            print(f"✅ English query test passed - Confidence: {result.confidence}")
            
        except Exception as e:
            pytest.fail(f"LLM service test failed: {str(e)}")
    
    def test_session_service(self):
        """Test session management"""
        print("💾 Testing session service...")
        
        try:
            session_service = SessionService()
            
            # Test session creation and retrieval
            session_info = session_service.get_session_info(self.test_session_id)
            print(f"✅ Session service test passed")
            
        except Exception as e:
            pytest.fail(f"Session service test failed: {str(e)}")
    
    def test_backend_health(self):
        """Test backend API health"""
        print("🏥 Testing backend health...")
        
        try:
            response = requests.get(f"{self.backend_url}/health", timeout=10)
            assert response.status_code == 200, f"Health check failed with status {response.status_code}"
            
            data = response.json()
            assert data.get("status") in ["healthy", "unhealthy"], "Invalid health status"
            print(f"✅ Backend health check passed - Status: {data.get('status')}")
            
        except requests.RequestException as e:
            print(f"⚠️ Backend not running or unreachable: {e}")
            pytest.skip("Backend not available for testing")
    
    def test_backend_query_endpoint(self):
        """Test backend query processing"""
        print("🔍 Testing backend query endpoint...")
        
        try:
            test_query = {
                "query": "ما هو أكثر منتج مبيعاً؟",
                "language": "ar",
                "session_id": self.test_session_id
            }
            
            response = requests.post(
                f"{self.backend_url}/query",
                json=test_query,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                assert "sql_query" in data, "Response should contain sql_query"
                assert "query_result" in data, "Response should contain query_result"
                assert "analysis" in data, "Response should contain analysis"
                print("✅ Backend query endpoint test passed")
            else:
                print(f"⚠️ Backend query failed with status {response.status_code}")
                print(f"Response: {response.text}")
                
        except requests.RequestException as e:
            print(f"⚠️ Backend not running or unreachable: {e}")
            pytest.skip("Backend not available for testing")
    
    def test_frontend_accessibility(self):
        """Test frontend accessibility"""
        print("🌐 Testing frontend accessibility...")
        
        try:
            response = requests.get(f"{self.frontend_url}", timeout=10)
            if response.status_code == 200:
                print("✅ Frontend is accessible")
            else:
                print(f"⚠️ Frontend returned status {response.status_code}")
                
        except requests.RequestException as e:
            print(f"⚠️ Frontend not running or unreachable: {e}")
            pytest.skip("Frontend not available for testing")

class TestSampleQueries:
    """Test sample queries to ensure system works correctly"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.backend_url = "http://127.0.0.1:8000"
        self.sample_queries = [
            {
                "query": "ما هو أكثر منتج مبيعاً؟",
                "language": "ar",
                "description": "Top selling product in Arabic"
            },
            {
                "query": "What are the total sales this month?",
                "language": "en", 
                "description": "Monthly sales in English"
            },
            {
                "query": "من هو العميل الأكثر شراءً؟",
                "language": "ar",
                "description": "Top customer in Arabic"
            },
            {
                "query": "Show me products that need restocking",
                "language": "en",
                "description": "Low stock products in English"
            }
        ]
    
    @pytest.mark.parametrize("query_data", [
        {"query": "ما هو أكثر منتج مبيعاً؟", "language": "ar"},
        {"query": "What are the total sales this month?", "language": "en"},
        {"query": "من هو العميل الأكثر شراءً؟", "language": "ar"},
        {"query": "Show me products that need restocking", "language": "en"}
    ])
    def test_sample_query(self, query_data):
        """Test individual sample queries"""
        print(f"🔍 Testing query: {query_data['query']}")
        
        try:
            response = requests.post(
                f"{self.backend_url}/query",
                json={
                    **query_data,
                    "session_id": f"test-{int(time.time())}"
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                assert data["sql_query"]["confidence"] > 0, "Confidence should be greater than 0"
                print(f"✅ Query test passed - Confidence: {data['sql_query']['confidence']:.2f}")
            else:
                print(f"⚠️ Query failed with status {response.status_code}")
                
        except requests.RequestException as e:
            print(f"⚠️ Backend not available: {e}")
            pytest.skip("Backend not available for testing")

def run_manual_tests():
    """Run manual tests without pytest"""
    print("🧪 Running Manual System Tests")
    print("=" * 50)
    
    # Test database
    print("\n1. Database Connection Test:")
    try:
        result = DatabaseUtils.test_connection()
        print(f"   {'✅' if result else '❌'} Database: {'Connected' if result else 'Failed'}")
    except Exception as e:
        print(f"   ❌ Database: Error - {e}")
    
    # Test LLM service
    print("\n2. LLM Service Test:")
    try:
        llm_service = LLMService()
        result = llm_service.generate_sql_query("ما هو أكثر منتج مبيعاً؟", "ar")
        print(f"   ✅ LLM Service: Working - Confidence: {result.confidence:.2f}")
        print(f"   📝 Generated SQL: {result.sql[:50]}...")
    except Exception as e:
        print(f"   ❌ LLM Service: Error - {e}")
    
    # Test session service
    print("\n3. Session Service Test:")
    try:
        session_service = SessionService()
        print("   ✅ Session Service: Working")
    except Exception as e:
        print(f"   ❌ Session Service: Error - {e}")
    
    # Test backend API
    print("\n4. Backend API Test:")
    try:
        response = requests.get("http://127.0.0.1:8000/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Backend API: {data.get('status', 'unknown')}")
        else:
            print(f"   ⚠️ Backend API: Status {response.status_code}")
    except Exception as e:
        print(f"   ❌ Backend API: Not running - {e}")
    
    # Test frontend
    print("\n5. Frontend Test:")
    try:
        response = requests.get("http://127.0.0.1:8080", timeout=5)
        if response.status_code == 200:
            print("   ✅ Frontend: Accessible")
        else:
            print(f"   ⚠️ Frontend: Status {response.status_code}")
    except Exception as e:
        print(f"   ❌ Frontend: Not running - {e}")
    
    print("\n" + "=" * 50)
    print("🏁 Manual tests completed!")

if __name__ == "__main__":
    run_manual_tests()
