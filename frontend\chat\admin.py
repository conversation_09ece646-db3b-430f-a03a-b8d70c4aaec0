from django.contrib import admin
from .models import ChatSession, QueryHistory


@admin.register(ChatSession)
class ChatSessionAdmin(admin.ModelAdmin):
    list_display = ('session_id', 'query_count', 'created_at', 'last_activity')
    list_filter = ('created_at', 'last_activity')
    search_fields = ('session_id',)
    readonly_fields = ('created_at', 'last_activity')


@admin.register(QueryHistory)
class QueryHistoryAdmin(admin.ModelAdmin):
    list_display = ('query_text_short', 'language', 'confidence', 'result_count', 'execution_time', 'created_at')
    list_filter = ('language', 'chart_type', 'created_at')
    search_fields = ('query_text', 'sql_query')
    readonly_fields = ('created_at',)
    
    def query_text_short(self, obj):
        return obj.query_text[:50] + "..." if len(obj.query_text) > 50 else obj.query_text
    query_text_short.short_description = "نص الاستعلام"
