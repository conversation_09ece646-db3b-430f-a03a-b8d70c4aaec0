"""
Session service for managing user conversations and history
"""
import logging
import sqlite3
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from backend.config import settings
from backend.models.schemas import AgentResponse, SessionInfo

logger = logging.getLogger(__name__)

class SessionService:
    """Service for managing user sessions and conversation history"""
    
    def __init__(self):
        self.db_path = settings.SESSION_DB_PATH
        self._init_database()
    
    def _init_database(self):
        """Initialize SQLite database for sessions"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Create sessions table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS sessions (
                        session_id TEXT PRIMARY KEY,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        query_count INTEGER DEFAULT 0
                    )
                """)
                
                # Create interactions table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS interactions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        original_query TEXT,
                        sql_query TEXT,
                        sql_explanation TEXT,
                        sql_confidence REAL,
                        result_data TEXT,
                        result_columns TEXT,
                        result_row_count INTEGER,
                        execution_time REAL,
                        analysis_summary TEXT,
                        analysis_insights TEXT,
                        chart_data TEXT,
                        chart_type TEXT,
                        FOREIGN KEY (session_id) REFERENCES sessions (session_id)
                    )
                """)
                
                conn.commit()
                logger.info("Session database initialized")
                
        except Exception as e:
            logger.error(f"Failed to initialize session database: {str(e)}")
            raise
    
    def save_interaction(self, session_id: str, response: AgentResponse):
        """
        Save interaction to database
        
        Args:
            session_id: Session identifier
            response: Agent response to save
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Insert or update session
                cursor.execute("""
                    INSERT OR REPLACE INTO sessions (session_id, last_activity, query_count)
                    VALUES (?, ?, COALESCE((SELECT query_count FROM sessions WHERE session_id = ?), 0) + 1)
                """, (session_id, datetime.now(), session_id))
                
                # Insert interaction
                cursor.execute("""
                    INSERT INTO interactions (
                        session_id, original_query, sql_query, sql_explanation, sql_confidence,
                        result_data, result_columns, result_row_count, execution_time,
                        analysis_summary, analysis_insights, chart_data, chart_type
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    session_id,
                    response.original_query,
                    response.sql_query.sql,
                    response.sql_query.explanation,
                    response.sql_query.confidence,
                    json.dumps(response.query_result.data, ensure_ascii=False),
                    json.dumps(response.query_result.columns, ensure_ascii=False),
                    response.query_result.row_count,
                    response.query_result.execution_time,
                    response.analysis.summary,
                    json.dumps(response.analysis.insights, ensure_ascii=False),
                    json.dumps(response.analysis.chart_data, ensure_ascii=False) if response.analysis.chart_data else None,
                    response.analysis.chart_type
                ))
                
                conn.commit()
                logger.info(f"Interaction saved for session {session_id}")
                
        except Exception as e:
            logger.error(f"Failed to save interaction: {str(e)}")
            raise
    
    def get_session_info(self, session_id: str) -> Optional[SessionInfo]:
        """
        Get session information
        
        Args:
            session_id: Session identifier
            
        Returns:
            SessionInfo object or None if not found
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT session_id, created_at, last_activity, query_count
                    FROM sessions
                    WHERE session_id = ?
                """, (session_id,))
                
                row = cursor.fetchone()
                if row:
                    return SessionInfo(
                        session_id=row[0],
                        created_at=datetime.fromisoformat(row[1]),
                        last_activity=datetime.fromisoformat(row[2]),
                        query_count=row[3]
                    )
                
                return None
                
        except Exception as e:
            logger.error(f"Failed to get session info: {str(e)}")
            return None
    
    def get_session_history(self, session_id: str) -> List[Dict[str, Any]]:
        """
        Get conversation history for a session
        
        Args:
            session_id: Session identifier
            
        Returns:
            List of interactions
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT 
                        timestamp, original_query, sql_query, sql_explanation, sql_confidence,
                        result_row_count, execution_time, analysis_summary, analysis_insights,
                        chart_type
                    FROM interactions
                    WHERE session_id = ?
                    ORDER BY timestamp ASC
                """, (session_id,))
                
                rows = cursor.fetchall()
                history = []
                
                for row in rows:
                    history.append({
                        'timestamp': row[0],
                        'original_query': row[1],
                        'sql_query': row[2],
                        'sql_explanation': row[3],
                        'sql_confidence': row[4],
                        'result_row_count': row[5],
                        'execution_time': row[6],
                        'analysis_summary': row[7],
                        'analysis_insights': json.loads(row[8]) if row[8] else [],
                        'chart_type': row[9]
                    })
                
                return history
                
        except Exception as e:
            logger.error(f"Failed to get session history: {str(e)}")
            return []
    
    def get_recent_sessions(self, limit: int = 10) -> List[SessionInfo]:
        """
        Get recent sessions
        
        Args:
            limit: Maximum number of sessions to return
            
        Returns:
            List of recent SessionInfo objects
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT session_id, created_at, last_activity, query_count
                    FROM sessions
                    ORDER BY last_activity DESC
                    LIMIT ?
                """, (limit,))
                
                rows = cursor.fetchall()
                sessions = []
                
                for row in rows:
                    sessions.append(SessionInfo(
                        session_id=row[0],
                        created_at=datetime.fromisoformat(row[1]),
                        last_activity=datetime.fromisoformat(row[2]),
                        query_count=row[3]
                    ))
                
                return sessions
                
        except Exception as e:
            logger.error(f"Failed to get recent sessions: {str(e)}")
            return []
    
    def delete_session(self, session_id: str) -> bool:
        """
        Delete a session and all its interactions
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Delete interactions first (foreign key constraint)
                cursor.execute("DELETE FROM interactions WHERE session_id = ?", (session_id,))
                
                # Delete session
                cursor.execute("DELETE FROM sessions WHERE session_id = ?", (session_id,))
                
                conn.commit()
                
                if cursor.rowcount > 0:
                    logger.info(f"Session {session_id} deleted successfully")
                    return True
                else:
                    logger.warning(f"Session {session_id} not found for deletion")
                    return False
                
        except Exception as e:
            logger.error(f"Failed to delete session: {str(e)}")
            return False
    
    def cleanup_old_sessions(self, days: int = 30) -> int:
        """
        Clean up sessions older than specified days
        
        Args:
            days: Number of days to keep sessions
            
        Returns:
            Number of sessions deleted
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Delete old interactions first
                cursor.execute("""
                    DELETE FROM interactions 
                    WHERE session_id IN (
                        SELECT session_id FROM sessions 
                        WHERE last_activity < datetime('now', '-{} days')
                    )
                """.format(days))
                
                # Delete old sessions
                cursor.execute("""
                    DELETE FROM sessions 
                    WHERE last_activity < datetime('now', '-{} days')
                """.format(days))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                logger.info(f"Cleaned up {deleted_count} old sessions")
                return deleted_count
                
        except Exception as e:
            logger.error(f"Failed to cleanup old sessions: {str(e)}")
            return 0
