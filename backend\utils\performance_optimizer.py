"""
Performance optimization utilities for the accounting agent system
"""
import logging
import time
import functools
from typing import Dict, Any, Callable
import asyncio
from concurrent.futures import ThreadPoolExecutor
import cachetools

logger = logging.getLogger(__name__)

class PerformanceOptimizer:
    """Performance optimization utilities"""
    
    def __init__(self):
        # Cache for SQL queries (TTL: 5 minutes)
        self.sql_cache = cachetools.TTLCache(maxsize=100, ttl=300)
        
        # Cache for LLM responses (TTL: 10 minutes)
        self.llm_cache = cachetools.TTLCache(maxsize=50, ttl=600)
        
        # Thread pool for CPU-intensive tasks
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
    
    def cache_sql_result(self, sql_hash: str, result: Any) -> None:
        """Cache SQL query result"""
        try:
            self.sql_cache[sql_hash] = result
            logger.debug(f"Cached SQL result for hash: {sql_hash}")
        except Exception as e:
            logger.warning(f"Failed to cache SQL result: {e}")
    
    def get_cached_sql_result(self, sql_hash: str) -> Any:
        """Get cached SQL result"""
        try:
            result = self.sql_cache.get(sql_hash)
            if result:
                logger.debug(f"Cache hit for SQL hash: {sql_hash}")
            return result
        except Exception as e:
            logger.warning(f"Failed to get cached SQL result: {e}")
            return None
    
    def cache_llm_response(self, query_hash: str, response: Any) -> None:
        """Cache LLM response"""
        try:
            self.llm_cache[query_hash] = response
            logger.debug(f"Cached LLM response for hash: {query_hash}")
        except Exception as e:
            logger.warning(f"Failed to cache LLM response: {e}")
    
    def get_cached_llm_response(self, query_hash: str) -> Any:
        """Get cached LLM response"""
        try:
            result = self.llm_cache.get(query_hash)
            if result:
                logger.debug(f"Cache hit for LLM hash: {query_hash}")
            return result
        except Exception as e:
            logger.warning(f"Failed to get cached LLM response: {e}")
            return None
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            "sql_cache": {
                "size": len(self.sql_cache),
                "maxsize": self.sql_cache.maxsize,
                "hits": getattr(self.sql_cache, 'hits', 0),
                "misses": getattr(self.sql_cache, 'misses', 0)
            },
            "llm_cache": {
                "size": len(self.llm_cache),
                "maxsize": self.llm_cache.maxsize,
                "hits": getattr(self.llm_cache, 'hits', 0),
                "misses": getattr(self.llm_cache, 'misses', 0)
            }
        }
    
    def clear_caches(self) -> None:
        """Clear all caches"""
        self.sql_cache.clear()
        self.llm_cache.clear()
        logger.info("All caches cleared")

# Global optimizer instance
optimizer = PerformanceOptimizer()

def timing_decorator(func: Callable) -> Callable:
    """Decorator to measure function execution time"""
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.info(f"{func.__name__} executed in {execution_time:.3f}s")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"{func.__name__} failed after {execution_time:.3f}s: {e}")
            raise
    
    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.info(f"{func.__name__} executed in {execution_time:.3f}s")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"{func.__name__} failed after {execution_time:.3f}s: {e}")
            raise
    
    return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

def cache_llm_response(func: Callable) -> Callable:
    """Decorator to cache LLM responses"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Create cache key from query and language
        query = kwargs.get('natural_query', args[1] if len(args) > 1 else '')
        language = kwargs.get('language', args[2] if len(args) > 2 else 'ar')
        cache_key = f"{query}_{language}"
        cache_hash = str(hash(cache_key))
        
        # Try to get from cache
        cached_result = optimizer.get_cached_llm_response(cache_hash)
        if cached_result:
            logger.info(f"LLM cache hit for query: {query[:50]}...")
            return cached_result
        
        # Execute function and cache result
        result = func(*args, **kwargs)
        optimizer.cache_llm_response(cache_hash, result)
        
        return result
    
    return wrapper

def cache_sql_result(func: Callable) -> Callable:
    """Decorator to cache SQL query results"""
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        # Create cache key from SQL query
        sql = kwargs.get('sql', args[1] if len(args) > 1 else '')
        cache_hash = str(hash(sql))
        
        # Try to get from cache
        cached_result = optimizer.get_cached_sql_result(cache_hash)
        if cached_result:
            logger.info(f"SQL cache hit for query: {sql[:50]}...")
            return cached_result
        
        # Execute function and cache result
        result = await func(*args, **kwargs)
        optimizer.cache_sql_result(cache_hash, result)
        
        return result
    
    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        # Create cache key from SQL query
        sql = kwargs.get('sql', args[1] if len(args) > 1 else '')
        cache_hash = str(hash(sql))
        
        # Try to get from cache
        cached_result = optimizer.get_cached_sql_result(cache_hash)
        if cached_result:
            logger.info(f"SQL cache hit for query: {sql[:50]}...")
            return cached_result
        
        # Execute function and cache result
        result = func(*args, **kwargs)
        optimizer.cache_sql_result(cache_hash, result)
        
        return result
    
    return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

class PerformanceMonitor:
    """Monitor system performance metrics"""
    
    def __init__(self):
        self.metrics = {
            "total_queries": 0,
            "successful_queries": 0,
            "failed_queries": 0,
            "avg_response_time": 0.0,
            "cache_hit_rate": 0.0,
            "total_response_time": 0.0
        }
    
    def record_query(self, success: bool, response_time: float):
        """Record query metrics"""
        self.metrics["total_queries"] += 1
        self.metrics["total_response_time"] += response_time
        
        if success:
            self.metrics["successful_queries"] += 1
        else:
            self.metrics["failed_queries"] += 1
        
        # Update average response time
        self.metrics["avg_response_time"] = (
            self.metrics["total_response_time"] / self.metrics["total_queries"]
        )
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        cache_stats = optimizer.get_cache_stats()
        
        # Calculate cache hit rate
        total_cache_requests = (
            cache_stats["sql_cache"]["hits"] + cache_stats["sql_cache"]["misses"] +
            cache_stats["llm_cache"]["hits"] + cache_stats["llm_cache"]["misses"]
        )
        
        if total_cache_requests > 0:
            cache_hits = cache_stats["sql_cache"]["hits"] + cache_stats["llm_cache"]["hits"]
            self.metrics["cache_hit_rate"] = cache_hits / total_cache_requests
        
        return {
            **self.metrics,
            "cache_stats": cache_stats,
            "success_rate": (
                self.metrics["successful_queries"] / max(self.metrics["total_queries"], 1)
            )
        }
    
    def reset_metrics(self):
        """Reset all metrics"""
        self.metrics = {
            "total_queries": 0,
            "successful_queries": 0,
            "failed_queries": 0,
            "avg_response_time": 0.0,
            "cache_hit_rate": 0.0,
            "total_response_time": 0.0
        }

# Global performance monitor
performance_monitor = PerformanceMonitor()

def optimize_database_query(sql: str) -> str:
    """Optimize SQL query for better performance"""
    # Basic SQL optimization hints
    optimizations = []
    
    # Add LIMIT if not present for large result sets
    if "SELECT" in sql.upper() and "LIMIT" not in sql.upper() and "TOP" not in sql.upper():
        if "ORDER BY" in sql.upper():
            sql = sql.replace("ORDER BY", "ORDER BY") + " LIMIT 1000"
        else:
            sql += " LIMIT 1000"
        optimizations.append("Added LIMIT clause")
    
    # Suggest indexes for WHERE clauses
    if "WHERE" in sql.upper():
        optimizations.append("Consider adding indexes on WHERE clause columns")
    
    if optimizations:
        logger.info(f"SQL optimizations applied: {', '.join(optimizations)}")
    
    return sql
