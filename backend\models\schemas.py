"""
Pydantic models for request/response schemas
"""
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from datetime import datetime

class QueryRequest(BaseModel):
    """Request model for natural language queries"""
    query: str = Field(..., description="Natural language query in Arabic or English")
    language: str = Field(default="ar", description="Language preference (ar/en)")
    session_id: Optional[str] = Field(None, description="Session ID for conversation tracking")

class SQLQuery(BaseModel):
    """Generated SQL query model"""
    sql: str = Field(..., description="Generated SQL query")
    explanation: str = Field(..., description="Explanation of the query")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence score")

class QueryResult(BaseModel):
    """Query execution result model"""
    data: List[Dict[str, Any]] = Field(..., description="Query result data")
    columns: List[str] = Field(..., description="Column names")
    row_count: int = Field(..., description="Number of rows returned")
    execution_time: float = Field(..., description="Query execution time in seconds")

class AnalysisResult(BaseModel):
    """Analysis and visualization result model"""
    summary: str = Field(..., description="Summary of the analysis")
    insights: List[str] = Field(..., description="Key insights from the data")
    chart_data: Optional[Dict[str, Any]] = Field(None, description="Chart configuration data")
    chart_type: Optional[str] = Field(None, description="Recommended chart type")

class AgentResponse(BaseModel):
    """Complete agent response model"""
    original_query: str = Field(..., description="Original user query")
    sql_query: SQLQuery = Field(..., description="Generated SQL query")
    query_result: QueryResult = Field(..., description="Query execution result")
    analysis: AnalysisResult = Field(..., description="Analysis and insights")
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")
    session_id: str = Field(..., description="Session ID")

class ErrorResponse(BaseModel):
    """Error response model"""
    error: str = Field(..., description="Error message")
    error_type: str = Field(..., description="Type of error")
    timestamp: datetime = Field(default_factory=datetime.now, description="Error timestamp")
    session_id: Optional[str] = Field(None, description="Session ID if available")

class SessionInfo(BaseModel):
    """Session information model"""
    session_id: str = Field(..., description="Unique session identifier")
    created_at: datetime = Field(..., description="Session creation time")
    last_activity: datetime = Field(..., description="Last activity time")
    query_count: int = Field(default=0, description="Number of queries in this session")

class HealthCheck(BaseModel):
    """Health check response model"""
    status: str = Field(..., description="Service status")
    timestamp: datetime = Field(default_factory=datetime.now, description="Check timestamp")
    database_connected: bool = Field(..., description="Database connection status")
    llm_available: bool = Field(..., description="LLM service availability")
