from django.db import models
from django.utils import timezone


class ChatSession(models.Model):
    """Model for chat sessions"""
    session_id = models.CharField(max_length=100, unique=True, verbose_name="معرف الجلسة")
    created_at = models.DateTimeField(default=timezone.now, verbose_name="تاريخ الإنشاء")
    last_activity = models.DateTimeField(auto_now=True, verbose_name="آخر نشاط")
    query_count = models.IntegerField(default=0, verbose_name="عدد الاستعلامات")
    
    class Meta:
        verbose_name = "جلسة محادثة"
        verbose_name_plural = "جلسات المحادثة"
        ordering = ['-last_activity']
    
    def __str__(self):
        return f"جلسة {self.session_id}"


class QueryHistory(models.Model):
    """Model for query history"""
    session = models.ForeignKey(ChatSession, on_delete=models.CASCADE, related_name='queries')
    query_text = models.TextField(verbose_name="نص الاستعلام")
    language = models.CharField(max_length=2, choices=[('ar', 'عربي'), ('en', 'English')], default='ar')
    sql_query = models.TextField(verbose_name="استعلام SQL")
    sql_explanation = models.TextField(verbose_name="شرح الاستعلام")
    confidence = models.FloatField(verbose_name="مستوى الثقة")
    result_count = models.IntegerField(verbose_name="عدد النتائج")
    execution_time = models.FloatField(verbose_name="وقت التنفيذ")
    chart_type = models.CharField(max_length=20, verbose_name="نوع الرسم البياني")
    created_at = models.DateTimeField(default=timezone.now, verbose_name="تاريخ الإنشاء")
    
    class Meta:
        verbose_name = "تاريخ الاستعلام"
        verbose_name_plural = "تاريخ الاستعلامات"
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.query_text[:50]}..."
