{"analysis": {"description": "تحليل جدولين مؤقتين يحتويان على بيانات مبيعات ومشتريات وتفاصيل الأصناف والعملاء والمخازن.", "tables": [{"tableName": "tbltemp_ItemsMain", "description": "جدول مؤقت يحتوي على تفاصيل حركات الأصناف مع معلومات شاملة عن العملاء، الفروع، الحسابات، المخازن، والأسعار.", "primaryKey": "ID", "columns": [{"name": "ID", "type": "bigint", "description": "المعرف الفريد للسجل."}, {"name": "ParentID", "type": "bigint", "description": "السجل الأب (مثلاً فاتورة رئيسية)."}, {"name": "RowVersion", "type": "timestamp", "description": "رقم إصدار الصف للكشف عن التحديثات المتزامنة."}, {"name": "DocumentID", "type": "bigint", "description": "معرف نوع المستند (مثلاً: 1 = فاتورة بيع)."}, {"name": "RecordNumber", "type": "bigint", "description": "الرقم التسلسلي للعملية."}, {"name": "RecordID", "type": "bigint", "description": "رقم العملية (مثل رقم الفاتورة)."}, {"name": "TheDate", "type": "datetime", "description": "تاريخ إصدار الفاتورة أو الحركة."}, {"name": "ClientID", "type": "bigint", "description": "معر<PERSON> العميل."}, {"name": "DistributorID", "type": "bigint", "description": "معرف المورد أو الموزع."}, {"name": "CurrencyID", "type": "bigint", "description": "معرف العملة."}, {"name": "TheMethodID", "type": "bigint", "description": "طريقة الدفع (نقداً، آجل...)."}, {"name": "Discount", "type": "numeric(18,6)", "description": "الخصم العام على السطر."}, {"name": "Notes", "type": "<PERSON><PERSON><PERSON>(255)", "description": "ملاحظات إضافية."}, {"name": "UserID", "type": "bigint", "description": "المستخدم الذي أدخل السجل."}, {"name": "BranchID", "type": "bigint", "description": "معرف الفرع."}, {"name": "TheYear", "type": "int", "description": "السنة المالية."}, {"name": "DocumentName", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم المستند (مثل: فاتورة بيع)."}, {"name": "TheNumber", "type": "bigint", "description": "رقم الفاتورة أو المستند."}, {"name": "ClientName", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم العميل."}, {"name": "DistributorName", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم المورد."}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم العملة."}, {"name": "TheMethod", "type": "<PERSON><PERSON><PERSON>(150)", "description": "وصف طريقة الدفع."}, {"name": "UserName", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم المستخدم."}, {"name": "BranchName", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم الفرع."}, {"name": "CategoryID", "type": "bigint", "description": "تصنيف الصنف."}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bigint", "description": "الرقم الأب (مثلاً رقم الفاتورة الأم)."}, {"name": "CategoryName", "type": "<PERSON><PERSON><PERSON>(200)", "description": "اسم التصنيف."}, {"name": "CategoryNumber", "type": "bigint", "description": "رقم التصنيف."}, {"name": "ItemID", "type": "bigint", "description": "معرف الصنف."}, {"name": "UnitID", "type": "bigint", "description": "وحدة القياس."}, {"name": "ItemNumber", "type": "bigint", "description": "رقم الصنف."}, {"name": "ItemName", "type": "<PERSON><PERSON><PERSON>(200)", "description": "اسم الصنف."}, {"name": "ItemTypeID", "type": "bigint", "description": "نوع الصنف (منتج، خدمة...)."}, {"name": "ItemType", "type": "<PERSON><PERSON><PERSON>(150)", "description": "وصف نوع الصنف."}, {"name": "ReorderPoint", "type": "numeric(18,4)", "description": "نقطة إعادة الطلب."}, {"name": "ISActive", "type": "bit", "description": "هل الصنف مفعل؟"}, {"name": "ISExpiry", "type": "bit", "description": "هل له صلاحية؟"}, {"name": "ExpiryPoint", "type": "bigint", "description": "عد<PERSON> الأيام لتحذير الصلاحية."}, {"name": "UnitName", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم الوحدة."}, {"name": "AccountFatherNumber", "type": "bigint", "description": "الحساب المحاسبي الأب."}, {"name": "Account<PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم الحساب."}, {"name": "AccountNumber", "type": "bigint", "description": "رقم الحساب."}, {"name": "CostCenterID", "type": "bigint", "description": "مركز التكلفة."}, {"name": "CostCenterName", "type": "<PERSON><PERSON><PERSON>(200)", "description": "اسم مركز التكلفة."}, {"name": "CostCenterNumber", "type": "bigint", "description": "رقم مركز التكلفة."}, {"name": "Barcode", "type": "<PERSON><PERSON><PERSON>(150)", "description": "الباركود."}, {"name": "UnitRank", "type": "bigint", "description": "ترتيب الوحدة (1 = صغيرة)."}, {"name": "ExchangeFactor", "type": "numeric(18,6)", "description": "معامل التحويل بين الوحدات."}, {"name": "PackageQuantity", "type": "numeric(18,6)", "description": "كمية التغليف (مثلاً 12 قطعة/كرتون)."}, {"name": "BarcodeID", "type": "bigint", "description": "معرف الباركود."}, {"name": "SerialNumber", "type": "<PERSON><PERSON><PERSON>(150)", "description": "الرقم التسلسلي للمنتج."}, {"name": "UnitPrice", "type": "numeric(18,6)", "description": "سعر الوحدة."}, {"name": "ItemD<PERSON>unt", "type": "numeric(18,6)", "description": "<PERSON>صم على الصنف."}, {"name": "McItemDiscountCurrencyMain", "type": "numeric(38,13)", "description": "الخصم بالعملة الأساسية."}, {"name": "Mc<PERSON>temDiscount", "type": "numeric(38,6)", "description": "الخصم بالعملة المحلية."}, {"name": "Quantity", "type": "numeric(18,6)", "description": "الكمية المباعة."}, {"name": "Bonus", "type": "numeric(18,6)", "description": "الكمية المجانية."}, {"name": "ExpiryDate", "type": "date", "description": "تاريخ انتهاء الصلاحية."}, {"name": "Amount", "type": "numeric(18,6)", "description": "المبلغ (الكمية × السعر)."}, {"name": "MCAmount", "type": "numeric(18,6)", "description": "المبلغ بالعملة المحلية."}, {"name": "MCAmountCurrencyMain", "type": "decimal(18,6)", "description": "المبلغ بالعملة الأساسية."}, {"name": "AccountID", "type": "bigint", "description": "معرف الح<PERSON><PERSON><PERSON> المحاسبي."}, {"name": "StoreID", "type": "bigint", "description": "معرف المخزن."}, {"name": "StoreName", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم المخزن."}, {"name": "PackageUnitID", "type": "bigint", "description": "وحدة التغليف."}, {"name": "PackageUnitName", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم وحدة التغليف."}, {"name": "NextParentID", "type": "bigint", "description": "السجل الأب التالي (تسلسل هرمي)."}, {"name": "ExchangePrice", "type": "numeric(18,6)", "description": "سعر التحويل (مثلاً سعر الكرتون)."}, {"name": "ExchangePriceCurrencyInvetory", "type": "decimal(18,6)", "description": "سعر التحويل بالعملة الخاصة بالمخزون."}]}, {"tableName": "tbltemp_Inv_MainInvoice", "description": "جدول مؤقت يحتوي على تفاصيل الفواتير مع تركيز على الكميات والأسعار بالدقة العالية.", "primaryKey": "ID", "columns": [{"name": "ID", "type": "bigint", "description": "المعرف الفريد للسجل."}, {"name": "DocumentName", "type": "<PERSON><PERSON><PERSON>(150)", "description": "نوع المستند (مثل: فاتورة شراء)."}, {"name": "RecordID", "type": "bigint", "description": "رقم العملية (مثل رقم الفاتورة)."}, {"name": "TheNumber", "type": "bigint", "description": "الرقم الظاهر في الفاتورة."}, {"name": "SupplierName", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم المورد."}, {"name": "InvoiceID", "type": "bigint", "description": "معرف الفاتورة."}, {"name": "DetailsID", "type": "bigint", "description": "معرف تفاصيل البند."}, {"name": "TheDate", "type": "datetime", "description": "تاريخ الفاتورة."}, {"name": "CurrencyID", "type": "bigint", "description": "معرف العملة."}, {"name": "TheMethod", "type": "<PERSON><PERSON><PERSON>(150)", "description": "طريقة الدفع."}, {"name": "EnterTime", "type": "datetime", "description": "وقت إدخال الفاتورة."}, {"name": "ItemID", "type": "bigint", "description": "معرف الصنف."}, {"name": "UnitID", "type": "bigint", "description": "وحدة القياس."}, {"name": "UnitPrice", "type": "numeric(18,6)", "description": "سعر الوحدة."}, {"name": "Quantity", "type": "numeric(18,6)", "description": "الكمية."}, {"name": "Bonus", "type": "numeric(18,6)", "description": "الكمية المجانية."}, {"name": "TotalAmount", "type": "numeric(18,6)", "description": "إجمالي المبلغ."}, {"name": "MainUnitQuantity", "type": "numeric(37,12)", "description": "الكمية بالوحدة الأساسية (دقة عالية)."}, {"name": "MainUnitPrice", "type": "numeric(38,20)", "description": "السعر بالوحدة الأساسية (دقة عالية)."}, {"name": "MainUnitID", "type": "bigint", "description": "معرف الوحدة الأساسية."}, {"name": "StoreID", "type": "bigint", "description": "المخزن."}, {"name": "BranchID", "type": "bigint", "description": "الفرع."}, {"name": "ExchangeFactor", "type": "numeric(18,6)", "description": "معامل التحويل بين الوحدات."}, {"name": "ClientID", "type": "bigint", "description": "معر<PERSON> العميل."}, {"name": "MCAmount", "type": "decimal(38,13)", "description": "المبلغ بالعملة المحلية (دقة عالية)."}, {"name": "ExpiryDate", "type": "date", "description": "تاريخ الصلاحية."}, {"name": "MainUnitBonus", "type": "numeric(37,12)", "description": "الكمية المجانية بالوحدة الأساسية."}, {"name": "ExchangePrice", "type": "numeric(18,6)", "description": "سعر التحويل (مثلاً سعر الكرتون)."}, {"name": "DistributorID", "type": "bigint", "description": "معرف الموزع."}, {"name": "DistributorName", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم الموزع."}, {"name": "CostCenterID", "type": "bigint", "description": "مركز التكلفة."}, {"name": "CostCenterName", "type": "<PERSON><PERSON><PERSON>(200)", "description": "اسم مركز التكلفة."}, {"name": "TotalAmountByCurrencyInvetory", "type": "numeric(38,13)", "description": "الإجمالي بالعملة الخاصة بالمخزون."}, {"name": "NewSubItemEntryID", "type": "bigint", "description": "معرف جديد للبند الفرعي."}]}], "common_queries": [{"title": "أكثر المنتجات مبيعًا", "description": "أعلى 10 منتجات من حيث الكمية المباعة.", "sql": "SELECT TOP 10 ItemName, SUM(Quantity) AS TotalQty FROM tbltemp_ItemsMain WHERE DocumentName LIKE '%بيع%' GROUP BY ItemName ORDER BY TotalQty DESC;"}, {"title": "أكثر العملاء شراءً", "description": "أعلى 10 عملاء من حيث إجمالي المبيعات.", "sql": "SELECT TOP 10 ClientName, SUM(Amount) AS TotalSales FROM tbltemp_ItemsMain WHERE DocumentName LIKE '%بيع%' GROUP BY ClientName ORDER BY TotalSales DESC;"}, {"title": "تفاصيل مشتريات عميل معين", "description": "عرض جميع عمليات الشراء لعميل معين.", "parameters": ["ClientName"], "sql": "SELECT TheDate, DocumentName, ItemName, Quantity, UnitPrice, Amount FROM tbltemp_ItemsMain WHERE ClientName = 'أحمد محمد' AND DocumentName LIKE '%شراء%' ORDER BY TheDate DESC;"}, {"title": "تفاصيل مبيعات منتج معين", "description": "عرض جميع عمليات بيع منتج معين.", "parameters": ["ItemName"], "sql": "SELECT TheDate, ClientName, Quantity, UnitPrice, Amount, StoreName FROM tbltemp_ItemsMain WHERE ItemName = 'شاشة سامسونج 55 بوصة' ORDER BY TheDate DESC;"}, {"title": "إجمالي المبيعات اليومية", "description": "إجمالي المبيعات حسب التاريخ.", "sql": "SELECT CAST(TheDate AS DATE) AS SaleDate, SUM(Amount) AS DailySales FROM tbltemp_ItemsMain WHERE DocumentName LIKE '%بيع%' GROUP BY CAST(TheDate AS DATE) ORDER BY SaleDate;"}, {"title": "تحليل مخزون حسب المخزن", "description": "إجمالي الكمية لكل صنف في كل مخزن.", "sql": "SELECT StoreName, ItemName, SUM(Quantity) AS TotalStock FROM tbltemp_ItemsMain GROUP BY StoreName, ItemName ORDER BY StoreName, TotalStock DESC;"}, {"title": "الأصناف التي اقتربت صلاحيتها", "description": "عرض الأصناف ذات الصلاحية التي تنتهي خلال 30 يومًا.", "sql": "SELECT ItemName, ExpiryDate, DATEDIFF(day, GETDATE(), ExpiryDate) AS DaysLeft FROM tbltemp_ItemsMain WHERE ISExpiry = 1 AND ExpiryDate BETWEEN GETDATE() AND DATEADD(day, 30, GETDATE());"}, {"title": "إجمالي المشتريات من مورد معين", "description": "إجمالي ما تم شراؤه من مورد معين.", "parameters": ["SupplierName"], "sql": "SELECT SUM(TotalAmount) AS TotalPurchases FROM tbltemp_Inv_MainInvoice WHERE SupplierName = 'شركة التوزيع المثالية';"}, {"title": "متوسط سعر البيع للمنتج", "description": "حساب متوسط سعر بيع منتج معين.", "parameters": ["ItemName"], "sql": "SELECT ItemName, AVG(UnitPrice) AS AvgPrice FROM tbltemp_ItemsMain WHERE ItemName = 'لابتوب ديل' GROUP BY ItemName;"}, {"title": "عد<PERSON> الفواتير حسب الفرع", "description": "عدد الفواتير الصادرة من كل فرع.", "sql": "SELECT BranchName, COUNT(DISTINCT RecordID) AS InvoiceCount FROM tbltemp_ItemsMain GROUP BY BranchName;"}], "notes": ["الجدولان مؤقتان وقد يستخدمان في عمليات ETL أو تقارير مؤقتة.", "تم تضمين حقول نصية مثل ClientName وItemName لتسهيل التحليل دون الحاجة لـ JOIN.", "استخدام أنواع بيانات دقيقة جدًا (مثل numeric(38,20)) يشير إلى الحاجة للدقة في الحسابات المالية والتحويلات.", "يمكن دمج الجدولين عبر حقول مثل RecordID أو ItemID لتحليل شامل."]}}