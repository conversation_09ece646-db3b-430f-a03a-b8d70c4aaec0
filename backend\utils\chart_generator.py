"""
Chart generation utilities using Plotly
"""
import logging
from typing import Dict, Any, List, Optional
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.utils import PlotlyJSONEncoder
import json

logger = logging.getLogger(__name__)

class ChartGenerator:
    """Generate interactive charts using Plotly"""
    
    def __init__(self):
        self.default_colors = [
            '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
            '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
        ]
        self.arabic_font = 'Arial Unicode MS'
    
    def create_bar_chart(self, data: List[Dict], x_col: str, y_col: str, 
                        title: str = "", language: str = "ar") -> Dict[str, Any]:
        """Create bar chart"""
        try:
            df = pd.DataFrame(data)
            
            fig = px.bar(
                df, 
                x=x_col, 
                y=y_col,
                title=title,
                color_discrete_sequence=self.default_colors
            )
            
            # Update layout for Arabic support
            fig.update_layout(
                font=dict(family=self.arabic_font if language == "ar" else "Arial"),
                title_font_size=16,
                xaxis_title=x_col,
                yaxis_title=y_col,
                showlegend=False,
                height=400
            )
            
            # Format numbers
            fig.update_traces(
                texttemplate='%{y:,.0f}',
                textposition='outside'
            )
            
            return {
                'type': 'bar',
                'config': json.loads(json.dumps(fig, cls=PlotlyJSONEncoder)),
                'title': title
            }
            
        except Exception as e:
            logger.error(f"Error creating bar chart: {str(e)}")
            return self._create_error_chart(str(e), language)
    
    def create_pie_chart(self, data: List[Dict], labels_col: str, values_col: str,
                        title: str = "", language: str = "ar") -> Dict[str, Any]:
        """Create pie chart"""
        try:
            df = pd.DataFrame(data)
            
            fig = px.pie(
                df,
                names=labels_col,
                values=values_col,
                title=title,
                color_discrete_sequence=self.default_colors
            )
            
            # Update layout
            fig.update_layout(
                font=dict(family=self.arabic_font if language == "ar" else "Arial"),
                title_font_size=16,
                height=400
            )
            
            # Update traces for better display
            fig.update_traces(
                textposition='inside',
                textinfo='percent+label',
                hovertemplate='<b>%{label}</b><br>القيمة: %{value:,.0f}<br>النسبة: %{percent}<extra></extra>'
                if language == "ar" else '<b>%{label}</b><br>Value: %{value:,.0f}<br>Percent: %{percent}<extra></extra>'
            )
            
            return {
                'type': 'pie',
                'config': json.loads(json.dumps(fig, cls=PlotlyJSONEncoder)),
                'title': title
            }
            
        except Exception as e:
            logger.error(f"Error creating pie chart: {str(e)}")
            return self._create_error_chart(str(e), language)
    
    def create_line_chart(self, data: List[Dict], x_col: str, y_col: str,
                         title: str = "", language: str = "ar") -> Dict[str, Any]:
        """Create line chart"""
        try:
            df = pd.DataFrame(data)
            
            fig = px.line(
                df,
                x=x_col,
                y=y_col,
                title=title,
                markers=True,
                color_discrete_sequence=self.default_colors
            )
            
            # Update layout
            fig.update_layout(
                font=dict(family=self.arabic_font if language == "ar" else "Arial"),
                title_font_size=16,
                xaxis_title=x_col,
                yaxis_title=y_col,
                height=400
            )
            
            # Update traces
            fig.update_traces(
                line=dict(width=3),
                marker=dict(size=8),
                hovertemplate='<b>%{x}</b><br>القيمة: %{y:,.0f}<extra></extra>'
                if language == "ar" else '<b>%{x}</b><br>Value: %{y:,.0f}<extra></extra>'
            )
            
            return {
                'type': 'line',
                'config': json.loads(json.dumps(fig, cls=PlotlyJSONEncoder)),
                'title': title
            }
            
        except Exception as e:
            logger.error(f"Error creating line chart: {str(e)}")
            return self._create_error_chart(str(e), language)
    
    def create_scatter_chart(self, data: List[Dict], x_col: str, y_col: str,
                           title: str = "", language: str = "ar") -> Dict[str, Any]:
        """Create scatter plot"""
        try:
            df = pd.DataFrame(data)
            
            fig = px.scatter(
                df,
                x=x_col,
                y=y_col,
                title=title,
                color_discrete_sequence=self.default_colors
            )
            
            # Update layout
            fig.update_layout(
                font=dict(family=self.arabic_font if language == "ar" else "Arial"),
                title_font_size=16,
                xaxis_title=x_col,
                yaxis_title=y_col,
                height=400
            )
            
            # Update traces
            fig.update_traces(
                marker=dict(size=10, opacity=0.7),
                hovertemplate='<b>%{x}</b><br>%{y:,.0f}<extra></extra>'
            )
            
            return {
                'type': 'scatter',
                'config': json.loads(json.dumps(fig, cls=PlotlyJSONEncoder)),
                'title': title
            }
            
        except Exception as e:
            logger.error(f"Error creating scatter chart: {str(e)}")
            return self._create_error_chart(str(e), language)
    
    def create_table_chart(self, data: List[Dict], title: str = "", 
                          language: str = "ar") -> Dict[str, Any]:
        """Create interactive table"""
        try:
            if not data:
                return self._create_empty_chart(language)
            
            df = pd.DataFrame(data)
            
            # Create table
            fig = go.Figure(data=[go.Table(
                header=dict(
                    values=list(df.columns),
                    fill_color='paleturquoise',
                    align='center',
                    font=dict(family=self.arabic_font if language == "ar" else "Arial", size=12)
                ),
                cells=dict(
                    values=[df[col] for col in df.columns],
                    fill_color='lavender',
                    align='center',
                    font=dict(family=self.arabic_font if language == "ar" else "Arial", size=11)
                )
            )])
            
            fig.update_layout(
                title=title,
                font=dict(family=self.arabic_font if language == "ar" else "Arial"),
                height=400
            )
            
            return {
                'type': 'table',
                'config': json.loads(json.dumps(fig, cls=PlotlyJSONEncoder)),
                'title': title
            }
            
        except Exception as e:
            logger.error(f"Error creating table: {str(e)}")
            return self._create_error_chart(str(e), language)
    
    def create_multi_series_chart(self, data: List[Dict], x_col: str, 
                                 series_cols: List[str], chart_type: str = "bar",
                                 title: str = "", language: str = "ar") -> Dict[str, Any]:
        """Create multi-series chart"""
        try:
            df = pd.DataFrame(data)
            
            if chart_type == "bar":
                fig = go.Figure()
                
                for i, col in enumerate(series_cols):
                    fig.add_trace(go.Bar(
                        name=col,
                        x=df[x_col],
                        y=df[col],
                        marker_color=self.default_colors[i % len(self.default_colors)]
                    ))
                
                fig.update_layout(
                    title=title,
                    xaxis_title=x_col,
                    yaxis_title="القيم" if language == "ar" else "Values",
                    barmode='group',
                    font=dict(family=self.arabic_font if language == "ar" else "Arial"),
                    height=400
                )
                
            elif chart_type == "line":
                fig = go.Figure()
                
                for i, col in enumerate(series_cols):
                    fig.add_trace(go.Scatter(
                        name=col,
                        x=df[x_col],
                        y=df[col],
                        mode='lines+markers',
                        line=dict(color=self.default_colors[i % len(self.default_colors)], width=3),
                        marker=dict(size=8)
                    ))
                
                fig.update_layout(
                    title=title,
                    xaxis_title=x_col,
                    yaxis_title="القيم" if language == "ar" else "Values",
                    font=dict(family=self.arabic_font if language == "ar" else "Arial"),
                    height=400
                )
            
            return {
                'type': f'multi_{chart_type}',
                'config': json.loads(json.dumps(fig, cls=PlotlyJSONEncoder)),
                'title': title
            }
            
        except Exception as e:
            logger.error(f"Error creating multi-series chart: {str(e)}")
            return self._create_error_chart(str(e), language)
    
    def _create_error_chart(self, error_msg: str, language: str = "ar") -> Dict[str, Any]:
        """Create error chart"""
        title = "خطأ في إنشاء الرسم البياني" if language == "ar" else "Chart Generation Error"
        
        fig = go.Figure()
        fig.add_annotation(
            text=f"خطأ: {error_msg}" if language == "ar" else f"Error: {error_msg}",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=16, color="red")
        )
        
        fig.update_layout(
            title=title,
            xaxis=dict(visible=False),
            yaxis=dict(visible=False),
            height=300
        )
        
        return {
            'type': 'error',
            'config': json.loads(json.dumps(fig, cls=PlotlyJSONEncoder)),
            'title': title
        }
    
    def _create_empty_chart(self, language: str = "ar") -> Dict[str, Any]:
        """Create empty data chart"""
        title = "لا توجد بيانات للعرض" if language == "ar" else "No Data to Display"
        
        fig = go.Figure()
        fig.add_annotation(
            text="لا توجد بيانات" if language == "ar" else "No Data Available",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=16, color="gray")
        )
        
        fig.update_layout(
            title=title,
            xaxis=dict(visible=False),
            yaxis=dict(visible=False),
            height=300
        )
        
        return {
            'type': 'empty',
            'config': json.loads(json.dumps(fig, cls=PlotlyJSONEncoder)),
            'title': title
        }
