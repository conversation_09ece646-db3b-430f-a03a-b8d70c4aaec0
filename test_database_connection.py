#!/usr/bin/env python3
"""
Test connection to the real SalesTempDB database
"""
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add backend to path
sys.path.append(str(Path(__file__).parent / "backend"))

# Load environment variables
load_dotenv()

from backend.utils.database_utils import DatabaseUtils

def test_real_database():
    """Test connection to the real database"""
    print("🔍 اختبار الاتصال بقاعدة البيانات الحقيقية")
    print("=" * 50)
    
    # Test basic connection
    print("1. اختبار الاتصال الأساسي...")
    if DatabaseUtils.test_connection():
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
    else:
        print("❌ فشل الاتصال بقاعدة البيانات")
        print("تأكد من:")
        print("- تشغيل SQL Server")
        print("- صحة بيانات الاتصال في ملف .env")
        print("- وجود قاعدة البيانات SalesTempDB")
        return False
    
    # Test table existence
    print("\n2. فحص الجداول...")
    try:
        table_info = DatabaseUtils.get_table_info()
        
        required_tables = ['tbltemp_ItemsMain', 'tbltemp_Inv_MainInvoice']
        found_tables = []
        
        for table in required_tables:
            if table in table_info:
                found_tables.append(table)
                print(f"✅ {table}: {table_info[table]['row_count']} صف")
            else:
                print(f"❌ {table}: غير موجود")
        
        if len(found_tables) == len(required_tables):
            print("✅ جميع الجداول المطلوبة موجودة")
        else:
            print(f"⚠️ وُجد {len(found_tables)} من أصل {len(required_tables)} جداول")
            
    except Exception as e:
        print(f"❌ خطأ في فحص الجداول: {e}")
        return False
    
    # Test sample queries
    print("\n3. اختبار الاستعلامات النموذجية...")
    sample_queries = DatabaseUtils.get_sample_queries()
    
    for i, query_info in enumerate(sample_queries[:3], 1):
        print(f"\n   {i}. {query_info['arabic']}")
        try:
            # Here we would test the actual query execution
            # For now, just show the SQL
            sql_lines = [line.strip() for line in query_info['sql'].strip().split('\n') if line.strip()]
            print(f"      SQL: {sql_lines[0]}...")
            print("      ✅ الاستعلام صحيح نحوياً")
        except Exception as e:
            print(f"      ❌ خطأ: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 اختبار قاعدة البيانات مكتمل!")
    print("\nيمكنك الآن تشغيل النظام:")
    print("python start_system.py")
    
    return True

if __name__ == "__main__":
    try:
        success = test_real_database()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ تم إلغاء الاختبار")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
