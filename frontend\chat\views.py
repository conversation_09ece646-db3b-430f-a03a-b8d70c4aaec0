import json
import uuid
import requests
from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.conf import settings
from .models import ChatSession, QueryHistory


def index(request):
    """Main chat interface"""
    # Get or create session
    session_id = request.session.get('chat_session_id')
    if not session_id:
        session_id = str(uuid.uuid4())
        request.session['chat_session_id'] = session_id
        
        # Create session in database
        ChatSession.objects.get_or_create(
            session_id=session_id,
            defaults={'query_count': 0}
        )
    
    # Get recent queries for this session
    try:
        chat_session = ChatSession.objects.get(session_id=session_id)
        recent_queries = QueryHistory.objects.filter(session=chat_session)[:10]
    except ChatSession.DoesNotExist:
        recent_queries = []
    
    context = {
        'session_id': session_id,
        'recent_queries': recent_queries,
    }
    
    return render(request, 'chat/index.html', context)


@csrf_exempt
@require_http_methods(["POST"])
def process_query(request):
    """Process user query through backend API"""
    try:
        data = json.loads(request.body)
        query = data.get('query', '').strip()
        language = data.get('language', 'ar')
        session_id = data.get('session_id')
        
        if not query:
            return JsonResponse({
                'error': 'الاستعلام فارغ' if language == 'ar' else 'Empty query'
            }, status=400)
        
        # Call backend API
        backend_url = getattr(settings, 'BACKEND_API_URL', 'http://127.0.0.1:8000')
        api_response = requests.post(
            f"{backend_url}/query",
            json={
                'query': query,
                'language': language,
                'session_id': session_id
            },
            timeout=30
        )
        
        if api_response.status_code == 200:
            result = api_response.json()
            
            # Save to database
            try:
                chat_session, created = ChatSession.objects.get_or_create(
                    session_id=session_id,
                    defaults={'query_count': 0}
                )
                
                QueryHistory.objects.create(
                    session=chat_session,
                    query_text=query,
                    language=language,
                    sql_query=result['sql_query']['sql'],
                    sql_explanation=result['sql_query']['explanation'],
                    confidence=result['sql_query']['confidence'],
                    result_count=result['query_result']['row_count'],
                    execution_time=result['query_result']['execution_time'],
                    chart_type=result['analysis']['chart_type']
                )
                
                # Update session query count
                chat_session.query_count += 1
                chat_session.save()
                
            except Exception as e:
                print(f"Error saving to database: {e}")
            
            return JsonResponse(result)
        else:
            error_msg = 'خطأ في الخادم' if language == 'ar' else 'Server error'
            try:
                error_detail = api_response.json().get('detail', error_msg)
            except:
                error_detail = error_msg
                
            return JsonResponse({
                'error': error_detail
            }, status=api_response.status_code)
            
    except requests.RequestException as e:
        return JsonResponse({
            'error': 'خطأ في الاتصال بالخادم' if language == 'ar' else 'Connection error'
        }, status=500)
    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=500)


@require_http_methods(["GET"])
def get_session_history(request, session_id):
    """Get session history"""
    try:
        chat_session = ChatSession.objects.get(session_id=session_id)
        queries = QueryHistory.objects.filter(session=chat_session).order_by('-created_at')
        
        history = []
        for query in queries:
            history.append({
                'id': query.id,
                'query_text': query.query_text,
                'language': query.language,
                'sql_query': query.sql_query,
                'sql_explanation': query.sql_explanation,
                'confidence': query.confidence,
                'result_count': query.result_count,
                'execution_time': query.execution_time,
                'chart_type': query.chart_type,
                'created_at': query.created_at.isoformat()
            })
        
        return JsonResponse({
            'session_id': session_id,
            'query_count': chat_session.query_count,
            'history': history
        })
        
    except ChatSession.DoesNotExist:
        return JsonResponse({
            'error': 'الجلسة غير موجودة'
        }, status=404)


@require_http_methods(["GET"])
def health_check(request):
    """Health check endpoint"""
    try:
        # Check backend API
        backend_url = getattr(settings, 'BACKEND_API_URL', 'http://127.0.0.1:8000')
        response = requests.get(f"{backend_url}/health", timeout=5)
        backend_status = response.status_code == 200
    except:
        backend_status = False
    
    return JsonResponse({
        'status': 'healthy' if backend_status else 'unhealthy',
        'frontend': 'ok',
        'backend': 'ok' if backend_status else 'error',
        'database': 'ok'  # Django will handle DB errors
    })
