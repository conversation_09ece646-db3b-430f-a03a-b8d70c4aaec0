# Groq API Configuration
GROQ_API_KEY=your_groq_api_key_here

# SQL Server Database Configuration
DB_SERVER=localhost
DB_DATABASE=SalesTempDB
DB_USERNAME=myuser
DB_PASSWORD=Aa227520
DB_PORT=1433
DB_TRUST_CERTIFICATE=true
DB_DRIVER=ODBC Driver 17 for SQL Server

# Django Configuration
DJANGO_SECRET_KEY=your_django_secret_key_here
DJANGO_DEBUG=True
DJANGO_ALLOWED_HOSTS=localhost,127.0.0.1

# FastAPI Configuration
FASTAPI_HOST=127.0.0.1
FASTAPI_PORT=8000

# Session Configuration
SESSION_DB_PATH=./sessions.db

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log
