"""
Configuration settings for the accounting agent system
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Settings:
    """Application settings"""
    
    # Groq API Configuration
    GROQ_API_KEY: str = os.getenv("GROQ_API_KEY", "********************************************************")
    GROQ_MODEL: str = "qwen/qwen3-32b"
    
    # Database Configuration
    DB_SERVER: str = os.getenv("DB_SERVER", "AHMED\\SQLEXPRESS")
    DB_DATABASE: str = os.getenv("DB_DATABASE", "SalesTempDB")
    DB_USERNAME: str = os.getenv("DB_USERNAME", "")
    DB_PASSWORD: str = os.getenv("DB_PASSWORD", "")
    DB_PORT: str = os.getenv("DB_PORT", "1433")
    DB_TRUST_CERTIFICATE: str = os.getenv("DB_TRUST_CERTIFICATE", "true")
    DB_DRIVER: str = os.getenv("DB_DRIVER", "ODBC Driver 17 for SQL Server")
    DB_USE_WINDOWS_AUTH: str = os.getenv("DB_USE_WINDOWS_AUTH", "true")
    
    # FastAPI Configuration
    FASTAPI_HOST: str = os.getenv("FASTAPI_HOST", "127.0.0.1")
    FASTAPI_PORT: int = int(os.getenv("FASTAPI_PORT", "8000"))
    
    # Session Configuration
    SESSION_DB_PATH: str = os.getenv("SESSION_DB_PATH", "./sessions.db")
    
    # Logging Configuration
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE: str = os.getenv("LOG_FILE", "./logs/app.log")
    
    @property
    def database_url(self) -> str:
        """Get database connection URL"""
        return f"mssql+pyodbc://{self.DB_USERNAME}:{self.DB_PASSWORD}@{self.DB_SERVER}:{self.DB_PORT}/{self.DB_DATABASE}?driver={self.DB_DRIVER.replace(' ', '+')}&TrustServerCertificate={self.DB_TRUST_CERTIFICATE}"

# Global settings instance
settings = Settings()
