<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام الوكيل الذكي للاستعلامات المحاسبية{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Plotly.js -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    
    {% load static %}
    <link href="{% static 'chat/css/style.css' %}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{% url 'chat:index' %}">
                <i class="fas fa-robot me-2"></i>
                الوكيل الذكي المحاسبي
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'chat:index' %}">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showHelp()">
                            <i class="fas fa-question-circle me-1"></i>
                            المساعدة
                        </a>
                    </li>
                    <li class="nav-item">
                        <span class="nav-link" id="status-indicator">
                            <i class="fas fa-circle text-success me-1"></i>
                            متصل
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid py-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0 text-muted">
                © 2024 نظام الوكيل الذكي للاستعلامات المحاسبية - 
                مدعوم بـ <i class="fas fa-heart text-danger"></i> و 
                <strong>Groq AI</strong>
            </p>
        </div>
    </footer>

    <!-- Help Modal -->
    <div class="modal fade" id="helpModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-question-circle me-2"></i>
                        كيفية استخدام النظام
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-comments text-primary me-2"></i>أمثلة على الاستعلامات:</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <code>ما هو أكثر منتج مبيعاً؟</code>
                                </li>
                                <li class="mb-2">
                                    <code>ما إجمالي المبيعات هذا الشهر؟</code>
                                </li>
                                <li class="mb-2">
                                    <code>من هو العميل الأكثر شراءً؟</code>
                                </li>
                                <li class="mb-2">
                                    <code>ما هي المنتجات التي تحتاج إعادة تخزين؟</code>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-lightbulb text-warning me-2"></i>نصائح:</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-1"></i>
                                    اكتب استعلامك بوضوح
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-1"></i>
                                    يمكنك الكتابة بالعربية أو الإنجليزية
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-1"></i>
                                    النظام يفهم الاستعلامات المحاسبية
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-1"></i>
                                    ستحصل على رسوم بيانية تفاعلية
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    {% block extra_js %}{% endblock %}

    <script>
        function showHelp() {
            new bootstrap.Modal(document.getElementById('helpModal')).show();
        }

        // Check system status
        function checkStatus() {
            fetch('/api/health/')
                .then(response => response.json())
                .then(data => {
                    const indicator = document.getElementById('status-indicator');
                    if (data.status === 'healthy') {
                        indicator.innerHTML = '<i class="fas fa-circle text-success me-1"></i>متصل';
                    } else {
                        indicator.innerHTML = '<i class="fas fa-circle text-danger me-1"></i>غير متصل';
                    }
                })
                .catch(() => {
                    const indicator = document.getElementById('status-indicator');
                    indicator.innerHTML = '<i class="fas fa-circle text-warning me-1"></i>خطأ';
                });
        }

        // Check status every 30 seconds
        setInterval(checkStatus, 30000);
        checkStatus();
    </script>
</body>
</html>
