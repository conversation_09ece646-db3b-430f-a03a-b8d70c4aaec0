// Chat functionality for the accounting agent system

class ChatInterface {
    constructor(sessionId) {
        this.sessionId = sessionId;
        this.chatMessages = document.getElementById('chat-messages');
        this.queryInput = document.getElementById('query-input');
        this.languageSelect = document.getElementById('language-select');
        this.sendBtn = document.getElementById('send-btn');
        this.chatForm = document.getElementById('chat-form');
        
        this.initializeEventListeners();
        this.loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    }
    
    initializeEventListeners() {
        // Form submission
        this.chatForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.sendMessage();
        });
        
        // Enter key to send
        this.queryInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // Auto-resize input
        this.queryInput.addEventListener('input', () => {
            this.queryInput.style.height = 'auto';
            this.queryInput.style.height = this.queryInput.scrollHeight + 'px';
        });
    }
    
    async sendMessage() {
        const query = this.queryInput.value.trim();
        const language = this.languageSelect.value;
        
        if (!query) {
            this.showError('يرجى كتابة استعلام', 'Please enter a query');
            return;
        }
        
        // Disable input and show loading
        this.setLoading(true);
        
        // Add user message to chat
        this.addMessage(query, 'user');
        
        // Clear input
        this.queryInput.value = '';
        this.queryInput.style.height = 'auto';
        
        // Show typing indicator
        this.showTypingIndicator();
        
        try {
            const response = await fetch('/api/query/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    query: query,
                    language: language,
                    session_id: this.sessionId
                })
            });
            
            const data = await response.json();
            
            // Remove typing indicator
            this.hideTypingIndicator();
            
            if (response.ok) {
                this.handleSuccessResponse(data);
            } else {
                this.handleErrorResponse(data);
            }
            
        } catch (error) {
            this.hideTypingIndicator();
            this.handleNetworkError(error);
        } finally {
            this.setLoading(false);
        }
    }
    
    handleSuccessResponse(data) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'chat-message bot';
        
        const language = this.languageSelect.value;
        
        messageDiv.innerHTML = `
            <div class="message-bubble bot">
                <!-- Query Summary -->
                <div class="mb-3">
                    <h6 class="mb-2">
                        <i class="fas fa-search me-2"></i>
                        ${language === 'ar' ? 'نتائج الاستعلام' : 'Query Results'}
                    </h6>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="badge bg-primary">${data.query_result.row_count} ${language === 'ar' ? 'نتيجة' : 'results'}</span>
                        <span class="badge ${this.getConfidenceBadgeClass(data.sql_query.confidence)}">
                            ${(data.sql_query.confidence * 100).toFixed(1)}% ${language === 'ar' ? 'ثقة' : 'confidence'}
                        </span>
                        <small class="text-muted">${data.query_result.execution_time.toFixed(2)}s</small>
                    </div>
                </div>
                
                <!-- SQL Query -->
                <div class="sql-query">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">
                            <i class="fas fa-database me-2"></i>
                            ${language === 'ar' ? 'استعلام SQL' : 'SQL Query'}
                        </h6>
                        <button class="btn btn-sm btn-outline-secondary copy-btn" onclick="copyToClipboard(this)" data-text="${this.escapeHtml(data.sql_query.sql)}">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                    <pre class="mb-0"><code>${this.escapeHtml(data.sql_query.sql)}</code></pre>
                </div>
                
                <!-- SQL Explanation -->
                <div class="sql-explanation">
                    <h6 class="mb-2">
                        <i class="fas fa-lightbulb me-2"></i>
                        ${language === 'ar' ? 'شرح الاستعلام' : 'Query Explanation'}
                    </h6>
                    <p class="mb-0">${this.escapeHtml(data.sql_query.explanation)}</p>
                </div>
                
                <!-- Analysis Summary -->
                <div class="analysis-summary mb-3">
                    <h6 class="mb-2">
                        <i class="fas fa-chart-line me-2"></i>
                        ${language === 'ar' ? 'ملخص التحليل' : 'Analysis Summary'}
                    </h6>
                    <p class="mb-0">${this.escapeHtml(data.analysis.summary)}</p>
                </div>
                
                <!-- Insights -->
                ${data.analysis.insights && data.analysis.insights.length > 0 ? `
                <div class="insights mb-3">
                    <h6 class="mb-2">
                        <i class="fas fa-brain me-2"></i>
                        ${language === 'ar' ? 'رؤى ذكية' : 'Smart Insights'}
                    </h6>
                    <ul class="list-unstyled">
                        ${data.analysis.insights.map(insight => `<li class="mb-1"><i class="fas fa-arrow-left me-2 text-primary"></i>${this.escapeHtml(insight)}</li>`).join('')}
                    </ul>
                </div>
                ` : ''}
                
                <!-- Chart Container -->
                <div class="chart-container" id="chart-${Date.now()}">
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">${language === 'ar' ? 'جاري تحميل الرسم البياني...' : 'Loading chart...'}</span>
                        </div>
                    </div>
                </div>
                
                <div class="message-time">
                    ${new Date().toLocaleTimeString('ar-SA')}
                </div>
            </div>
        `;
        
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
        
        // Render chart
        setTimeout(() => {
            this.renderChart(data.analysis.chart_data, `chart-${Date.now() - 1}`);
        }, 100);
    }
    
    renderChart(chartData, containerId) {
        const container = document.getElementById(containerId);
        if (!container || !chartData) {
            container.innerHTML = '<p class="text-muted text-center">لا يوجد رسم بياني متاح</p>';
            return;
        }
        
        try {
            if (chartData.type === 'table') {
                this.renderTable(chartData, container);
            } else {
                // Use Plotly for other chart types
                Plotly.newPlot(container, chartData.config.data, chartData.config.layout, {
                    responsive: true,
                    displayModeBar: true,
                    modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
                    locale: 'ar'
                });
            }
        } catch (error) {
            console.error('Error rendering chart:', error);
            container.innerHTML = '<p class="text-danger text-center">خطأ في عرض الرسم البياني</p>';
        }
    }
    
    renderTable(chartData, container) {
        if (!chartData.data || chartData.data.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">لا توجد بيانات للعرض</p>';
            return;
        }
        
        const columns = chartData.columns || Object.keys(chartData.data[0]);
        
        let tableHtml = `
            <div class="data-table">
                <table class="table table-striped table-hover mb-0">
                    <thead>
                        <tr>
                            ${columns.map(col => `<th>${this.escapeHtml(col)}</th>`).join('')}
                        </tr>
                    </thead>
                    <tbody>
                        ${chartData.data.map(row => `
                            <tr>
                                ${columns.map(col => `<td>${this.escapeHtml(String(row[col] || ''))}</td>`).join('')}
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
        
        container.innerHTML = tableHtml;
    }
    
    handleErrorResponse(data) {
        const errorMessage = data.error || 'حدث خطأ غير متوقع';
        this.addMessage(errorMessage, 'error');
    }
    
    handleNetworkError(error) {
        console.error('Network error:', error);
        const language = this.languageSelect.value;
        const errorMessage = language === 'ar' 
            ? 'خطأ في الاتصال بالخادم. يرجى المحاولة مرة أخرى.'
            : 'Connection error. Please try again.';
        this.addMessage(errorMessage, 'error');
    }
    
    addMessage(content, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message ${type}`;
        
        if (type === 'error') {
            messageDiv.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${this.escapeHtml(content)}
                </div>
            `;
        } else {
            messageDiv.innerHTML = `
                <div class="message-bubble ${type}">
                    ${this.escapeHtml(content)}
                    <div class="message-time">
                        ${new Date().toLocaleTimeString('ar-SA')}
                    </div>
                </div>
            `;
        }
        
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }
    
    showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'typing-indicator';
        typingDiv.id = 'typing-indicator';
        typingDiv.innerHTML = `
            <i class="fas fa-robot me-2"></i>
            <span>جاري التفكير...</span>
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        `;
        
        this.chatMessages.appendChild(typingDiv);
        this.scrollToBottom();
    }
    
    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }
    
    setLoading(loading) {
        this.sendBtn.disabled = loading;
        this.queryInput.disabled = loading;
        
        if (loading) {
            this.loadingModal.show();
            this.sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
        } else {
            this.loadingModal.hide();
            this.sendBtn.innerHTML = '<i class="fas fa-paper-plane"></i> إرسال';
        }
    }
    
    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }
    
    getConfidenceBadgeClass(confidence) {
        if (confidence >= 0.8) return 'bg-success';
        if (confidence >= 0.6) return 'bg-warning';
        return 'bg-danger';
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }
    
    showError(arMessage, enMessage) {
        const language = this.languageSelect.value;
        const message = language === 'ar' ? arMessage : enMessage;
        this.addMessage(message, 'error');
    }
}

// Utility functions
function copyToClipboard(button) {
    const text = button.getAttribute('data-text');
    navigator.clipboard.writeText(text).then(() => {
        const originalIcon = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i>';
        setTimeout(() => {
            button.innerHTML = originalIcon;
        }, 2000);
    });
}

// Initialize chat when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (typeof sessionId !== 'undefined') {
        window.chatInterface = new ChatInterface(sessionId);
    }
});
