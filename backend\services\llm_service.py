"""
LLM Service for natural language to SQL conversion using LangChain with Groq
"""
import logging
from langchain_groq import ChatGroq
from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.pydantic_v1 import BaseModel, Field
from backend.config import settings
from backend.models.schemas import SQLQuery

logger = logging.getLogger(__name__)

class SQLQueryOutput(BaseModel):
    """Pydantic model for LLM output parsing"""
    sql: str = Field(description="Generated SQL query")
    explanation: str = Field(description="Explanation of the query")
    confidence: float = Field(description="Confidence score between 0 and 1")

class LLMService:
    """Service for interacting with Groq LLM using LangChain"""

    def __init__(self):
        self.llm = ChatGroq(
            groq_api_key=settings.GROQ_API_KEY,
            model_name=settings.GROQ_MODEL,
            temperature=0.1,  # Low temperature for consistent SQL generation
            max_tokens=1000
        )
        self.output_parser = JsonOutputParser(pydantic_object=SQLQueryOutput)
        self._setup_prompts()

    def _setup_prompts(self):
        """Setup LangChain prompts for Arabic and English"""
        # Arabic system prompt
        self.arabic_system_prompt = """أنت خبير في قواعد البيانات المحاسبية وتحويل الاستعلامات الطبيعية إلى SQL.

مهمتك:
1. تحليل الاستعلام الطبيعي باللغة العربية
2. تحويله إلى استعلام SQL صحيح
3. تقديم شرح للاستعلام
4. تقدير مستوى الثقة في الاستعلام

قاعدة البيانات تحتوي على الجداول التالية:
- customers (العملاء): customer_id, name, email, phone, address
- products (المنتجات): product_id, name, price, category, stock_quantity
- sales (المبيعات): sale_id, customer_id, product_id, quantity, sale_date, total_amount
- invoices (الفواتير): invoice_id, customer_id, invoice_date, total_amount, status
- payments (المدفوعات): payment_id, invoice_id, payment_date, amount, payment_method

{format_instructions}"""

        # English system prompt
        self.english_system_prompt = """You are an expert in accounting databases and converting natural language queries to SQL.

Your task:
1. Analyze the natural language query in English
2. Convert it to a correct SQL query
3. Provide an explanation of the query
4. Estimate confidence level

Database schema:
- customers: customer_id, name, email, phone, address
- products: product_id, name, price, category, stock_quantity
- sales: sale_id, customer_id, product_id, quantity, sale_date, total_amount
- invoices: invoice_id, customer_id, invoice_date, total_amount, status
- payments: payment_id, invoice_id, payment_date, amount, payment_method

{format_instructions}"""

        # Create prompt templates
        self.arabic_prompt = ChatPromptTemplate.from_messages([
            SystemMessagePromptTemplate.from_template(self.arabic_system_prompt),
            HumanMessagePromptTemplate.from_template("الاستعلام: {query}\n\nحول هذا الاستعلام إلى SQL واشرحه:")
        ])

        self.english_prompt = ChatPromptTemplate.from_messages([
            SystemMessagePromptTemplate.from_template(self.english_system_prompt),
            HumanMessagePromptTemplate.from_template("Query: {query}\n\nConvert this query to SQL and explain it:")
        ])

    def generate_sql_query(self, natural_query: str, language: str = "ar") -> SQLQuery:
        """
        Convert natural language query to SQL using LangChain

        Args:
            natural_query: User's natural language query
            language: Language preference (ar/en)

        Returns:
            SQLQuery object with generated SQL and metadata
        """
        try:
            # Select appropriate prompt template
            prompt_template = self.arabic_prompt if language == "ar" else self.english_prompt

            # Create the chain
            chain = prompt_template | self.llm | self.output_parser

            # Execute the chain
            result = chain.invoke({
                "query": natural_query,
                "format_instructions": self.output_parser.get_format_instructions()
            })

            # Convert to SQLQuery object
            return SQLQuery(
                sql=result["sql"],
                explanation=result["explanation"],
                confidence=float(result["confidence"])
            )

        except Exception as e:
            logger.error(f"Error generating SQL query: {str(e)}")
            # Fallback response
            return SQLQuery(
                sql="SELECT 1 as error",
                explanation="خطأ في تحليل الاستعلام" if language == "ar" else "Error parsing query",
                confidence=0.0
            )

