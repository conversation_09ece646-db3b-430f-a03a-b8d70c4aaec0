"""
LLM Service for natural language to SQL conversion using Groq with <PERSON><PERSON><PERSON><PERSON>
"""
import logging
import json
from typing import Dict, Any
from groq import <PERSON>roq
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.pydantic_v1 import BaseModel, Field
from backend.config import settings
from backend.models.schemas import SQLQuery
from backend.utils.performance_optimizer import timing_decorator, cache_llm_response

logger = logging.getLogger(__name__)

class SQLQueryOutput(BaseModel):
    """Pydantic model for LLM output parsing"""
    sql: str = Field(description="Generated SQL query")
    explanation: str = Field(description="Explanation of the query")
    confidence: float = Field(description="Confidence score between 0 and 1")
    tables_used: list = Field(description="List of database tables used")
    query_type: str = Field(description="Type of query (SELECT, INSERT, etc.)")

class LLMService:
    """Service for interacting with Groq LLM using LangChain patterns"""
    
    def __init__(self):
        self.client = Groq(api_key=settings.GROQ_API_KEY)
        self.model = settings.GROQ_MODEL
        self.output_parser = PydanticOutputParser(pydantic_object=SQLQueryOutput)
        self._setup_prompts()
    
    def _setup_prompts(self):
        """Setup prompt templates for Arabic and English"""
        
        # Database schema description
        schema_description = """
قاعدة البيانات تحتوي على الجداول التالية:

1. customers (العملاء):
   - customer_id: INT PRIMARY KEY - معرف العميل
   - name: NVARCHAR(100) - اسم العميل
   - email: NVARCHAR(100) - البريد الإلكتروني
   - phone: NVARCHAR(20) - رقم الهاتف
   - address: NVARCHAR(255) - العنوان
   - created_date: DATETIME - تاريخ إنشاء الحساب

2. products (المنتجات):
   - product_id: INT PRIMARY KEY - معرف المنتج
   - name: NVARCHAR(100) - اسم المنتج
   - price: DECIMAL(10,2) - سعر المنتج
   - category: NVARCHAR(50) - فئة المنتج
   - stock_quantity: INT - الكمية المتوفرة
   - created_date: DATETIME - تاريخ إضافة المنتج

3. sales (المبيعات):
   - sale_id: INT PRIMARY KEY - معرف البيع
   - customer_id: INT - معرف العميل (مفتاح خارجي)
   - product_id: INT - معرف المنتج (مفتاح خارجي)
   - quantity: INT - الكمية المباعة
   - unit_price: DECIMAL(10,2) - سعر الوحدة
   - total_amount: DECIMAL(10,2) - المبلغ الإجمالي
   - sale_date: DATETIME - تاريخ البيع
   - salesperson: NVARCHAR(100) - اسم البائع

4. invoices (الفواتير):
   - invoice_id: INT PRIMARY KEY - معرف الفاتورة
   - customer_id: INT - معرف العميل (مفتاح خارجي)
   - invoice_date: DATETIME - تاريخ الفاتورة
   - total_amount: DECIMAL(10,2) - المبلغ الإجمالي
   - status: NVARCHAR(20) - حالة الفاتورة

5. payments (المدفوعات):
   - payment_id: INT PRIMARY KEY - معرف الدفع
   - invoice_id: INT - معرف الفاتورة (مفتاح خارجي)
   - payment_date: DATETIME - تاريخ الدفع
   - amount: DECIMAL(10,2) - مبلغ الدفع
   - payment_method: NVARCHAR(50) - طريقة الدفع
"""
        
        # Arabic prompt template
        self.arabic_prompt = PromptTemplate(
            input_variables=["query"],
            template=f"""أنت خبير في قواعد البيانات المحاسبية وتحويل الاستعلامات الطبيعية إلى SQL.

{schema_description}

مهامك:
1. تحليل الاستعلام الطبيعي باللغة العربية بعناية
2. تحويله إلى استعلام SQL صحيح ومحسن
3. تقديم شرح واضح للاستعلام باللغة العربية
4. تقدير مستوى الثقة في دقة الاستعلام
5. تحديد الجداول المستخدمة ونوع الاستعلام

قواعد مهمة:
- استخدم أسماء الجداول والأعمدة الصحيحة فقط
- تأكد من صحة العلاقات بين الجداول (Foreign Keys)
- استخدم JOIN عند الحاجة لربط الجداول
- أضف WHERE clauses مناسبة للتصفية
- استخدم GROUP BY و ORDER BY عند الحاجة
- تجنب استعلامات DELETE أو UPDATE إلا إذا طُلب صراحة

{{format_instructions}}

الاستعلام: {{query}}

حول هذا الاستعلام إلى SQL مع الشرح:"""
        )
        
        # English prompt template
        self.english_prompt = PromptTemplate(
            input_variables=["query"],
            template=f"""You are an expert in accounting databases and converting natural language queries to SQL.

Database Schema:
- customers: customer_id (PK), name, email, phone, address, created_date
- products: product_id (PK), name, price, category, stock_quantity, created_date
- sales: sale_id (PK), customer_id (FK), product_id (FK), quantity, unit_price, total_amount, sale_date, salesperson
- invoices: invoice_id (PK), customer_id (FK), invoice_date, total_amount, status
- payments: payment_id (PK), invoice_id (FK), payment_date, amount, payment_method

Your tasks:
1. Carefully analyze the natural language query in English
2. Convert it to a correct and optimized SQL query
3. Provide a clear explanation of the query in English
4. Estimate confidence level in the query accuracy
5. Identify tables used and query type

Important rules:
- Use only correct table and column names from the schema
- Ensure proper relationships between tables (Foreign Keys)
- Use JOIN when needed to connect tables
- Add appropriate WHERE clauses for filtering
- Use GROUP BY and ORDER BY when needed
- Avoid DELETE or UPDATE queries unless explicitly requested

{{format_instructions}}

Query: {{query}}

Convert this query to SQL with explanation:"""
        )
    
    @timing_decorator
    @cache_llm_response
    def generate_sql_query(self, natural_query: str, language: str = "ar") -> SQLQuery:
        """
        Convert natural language query to SQL using Groq with LangChain patterns
        
        Args:
            natural_query: User's natural language query
            language: Language preference (ar/en)
            
        Returns:
            SQLQuery object with generated SQL and metadata
        """
        try:
            # Select appropriate prompt template
            prompt_template = self.arabic_prompt if language == "ar" else self.english_prompt
            
            # Format the prompt
            formatted_prompt = prompt_template.format(
                query=natural_query,
                format_instructions=self.output_parser.get_format_instructions()
            )
            
            # Call Groq API
            completion = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "user", "content": formatted_prompt}
                ],
                temperature=0.1,  # Low temperature for consistent SQL generation
                max_tokens=1000,
                top_p=0.95
            )
            
            # Get response content
            response_content = completion.choices[0].message.content or ""

            # Parse the response using LangChain output parser
            try:
                parsed_output = self.output_parser.parse(response_content)

                return SQLQuery(
                    sql=parsed_output.sql,
                    explanation=parsed_output.explanation,
                    confidence=float(parsed_output.confidence)
                )
            except Exception as parse_error:
                logger.warning(f"Failed to parse with LangChain parser: {parse_error}")
                # Fallback to manual JSON parsing
                return self._manual_parse_response(response_content, language)
            
        except Exception as e:
            logger.error(f"Error generating SQL query: {str(e)}")
            # Fallback response
            return SQLQuery(
                sql="SELECT 1 as error",
                explanation="خطأ في تحليل الاستعلام" if language == "ar" else "Error parsing query",
                confidence=0.0
            )
    
    def _manual_parse_response(self, response: str, language: str) -> SQLQuery:
        """Manual parsing as fallback when LangChain parser fails"""
        try:
            # Try to extract JSON from response
            if "```json" in response:
                json_start = response.find("```json") + 7
                json_end = response.find("```", json_start)
                json_str = response[json_start:json_end].strip()
            elif "{" in response and "}" in response:
                json_start = response.find("{")
                json_end = response.rfind("}") + 1
                json_str = response[json_start:json_end]
            else:
                raise ValueError("No JSON found in response")
            
            # Parse JSON
            parsed = json.loads(json_str)
            
            return SQLQuery(
                sql=parsed.get("sql", ""),
                explanation=parsed.get("explanation", ""),
                confidence=float(parsed.get("confidence", 0.5))
            )
            
        except Exception as e:
            logger.error(f"Manual parsing also failed: {str(e)}")
            # Final fallback
            return SQLQuery(
                sql="SELECT 1 as error",
                explanation="خطأ في تحليل الاستعلام" if language == "ar" else "Error parsing query",
                confidence=0.0
            )
