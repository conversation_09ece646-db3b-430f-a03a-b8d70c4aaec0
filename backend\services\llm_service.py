"""
LLM Service for natural language to SQL conversion using Groq
"""
import json
import logging
from typing import Dict, Any
from groq import Groq
from backend.config import settings
from backend.models.schemas import SQLQuery

logger = logging.getLogger(__name__)

class LLMService:
    """Service for interacting with Groq LLM"""
    
    def __init__(self):
        self.client = Groq(api_key=settings.GROQ_API_KEY)
        self.model = settings.GROQ_MODEL
        
    def generate_sql_query(self, natural_query: str, language: str = "ar") -> SQLQuery:
        """
        Convert natural language query to SQL
        
        Args:
            natural_query: User's natural language query
            language: Language preference (ar/en)
            
        Returns:
            SQLQuery object with generated SQL and metadata
        """
        try:
            # Create system prompt based on language
            system_prompt = self._get_system_prompt(language)
            
            # Create user prompt
            user_prompt = self._create_user_prompt(natural_query, language)
            
            # Call Groq API
            completion = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1,  # Low temperature for consistent SQL generation
                max_tokens=1000,
                top_p=0.95
            )
            
            # Parse response
            response_content = completion.choices[0].message.content
            return self._parse_llm_response(response_content, language)
            
        except Exception as e:
            logger.error(f"Error generating SQL query: {str(e)}")
            raise Exception(f"Failed to generate SQL query: {str(e)}")
    
    def _get_system_prompt(self, language: str) -> str:
        """Get system prompt based on language"""
        if language == "ar":
            return """أنت خبير في قواعد البيانات المحاسبية وتحويل الاستعلامات الطبيعية إلى SQL.

مهمتك:
1. تحليل الاستعلام الطبيعي باللغة العربية
2. تحويله إلى استعلام SQL صحيح
3. تقديم شرح للاستعلام
4. تقدير مستوى الثقة في الاستعلام

قاعدة البيانات تحتوي على الجداول التالية:
- customers (العملاء): customer_id, name, email, phone, address
- products (المنتجات): product_id, name, price, category, stock_quantity
- sales (المبيعات): sale_id, customer_id, product_id, quantity, sale_date, total_amount
- invoices (الفواتير): invoice_id, customer_id, invoice_date, total_amount, status
- payments (المدفوعات): payment_id, invoice_id, payment_date, amount, payment_method

يجب أن تكون الإجابة بصيغة JSON مع الحقول التالية:
{
  "sql": "استعلام SQL",
  "explanation": "شرح الاستعلام بالعربية",
  "confidence": 0.95
}"""
        else:
            return """You are an expert in accounting databases and converting natural language queries to SQL.

Your task:
1. Analyze the natural language query in English
2. Convert it to a correct SQL query
3. Provide an explanation of the query
4. Estimate confidence level

Database schema:
- customers: customer_id, name, email, phone, address
- products: product_id, name, price, category, stock_quantity
- sales: sale_id, customer_id, product_id, quantity, sale_date, total_amount
- invoices: invoice_id, customer_id, invoice_date, total_amount, status
- payments: payment_id, invoice_id, payment_date, amount, payment_method

Response must be in JSON format with these fields:
{
  "sql": "SQL query",
  "explanation": "Query explanation in English",
  "confidence": 0.95
}"""
    
    def _create_user_prompt(self, query: str, language: str) -> str:
        """Create user prompt with the query"""
        if language == "ar":
            return f"الاستعلام: {query}\n\nحول هذا الاستعلام إلى SQL واشرحه:"
        else:
            return f"Query: {query}\n\nConvert this query to SQL and explain it:"
    
    def _parse_llm_response(self, response: str, language: str) -> SQLQuery:
        """Parse LLM response and create SQLQuery object"""
        try:
            # Try to extract JSON from response
            if "```json" in response:
                json_start = response.find("```json") + 7
                json_end = response.find("```", json_start)
                json_str = response[json_start:json_end].strip()
            elif "{" in response and "}" in response:
                json_start = response.find("{")
                json_end = response.rfind("}") + 1
                json_str = response[json_start:json_end]
            else:
                raise ValueError("No JSON found in response")
            
            # Parse JSON
            parsed = json.loads(json_str)
            
            return SQLQuery(
                sql=parsed.get("sql", ""),
                explanation=parsed.get("explanation", ""),
                confidence=float(parsed.get("confidence", 0.5))
            )
            
        except Exception as e:
            logger.error(f"Error parsing LLM response: {str(e)}")
            # Fallback response
            return SQLQuery(
                sql="SELECT 1 as error",
                explanation="خطأ في تحليل الاستعلام" if language == "ar" else "Error parsing query",
                confidence=0.0
            )
