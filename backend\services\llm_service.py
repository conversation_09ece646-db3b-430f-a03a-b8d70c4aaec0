"""
LLM Service for natural language to SQL conversion using Groq with <PERSON><PERSON><PERSON><PERSON>
"""
import logging
import json
from typing import Dict, Any
from groq import <PERSON>roq
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.pydantic_v1 import BaseModel, Field
from backend.config import settings
from backend.models.schemas import SQLQuery
from backend.utils.performance_optimizer import timing_decorator, cache_llm_response

logger = logging.getLogger(__name__)

class SQLQueryOutput(BaseModel):
    """Pydantic model for LLM output parsing"""
    sql: str = Field(description="Generated SQL query")
    explanation: str = Field(description="Explanation of the query")
    confidence: float = Field(description="Confidence score between 0 and 1")
    tables_used: list = Field(description="List of database tables used")
    query_type: str = Field(description="Type of query (SELECT, INSERT, etc.)")

class LLMService:
    """Service for interacting with Groq LLM using LangChain patterns"""
    
    def __init__(self):
        self.client = Groq(api_key=settings.GROQ_API_KEY)
        self.model = settings.GROQ_MODEL
        self.output_parser = PydanticOutputParser(pydantic_object=SQLQueryOutput)
        self._setup_prompts()
    
    def _setup_prompts(self):
        """Setup prompt templates for Arabic and English"""
        
        # Database schema description - Real database structure
        schema_description = """
قاعدة البيانات تحتوي على الجداول التالية:

1. tbltemp_ItemsMain (جدول حركات الأصناف الرئيسي):
   - ID: bigint PRIMARY KEY - المعرف الفريد للسجل
   - DocumentName: varchar(150) - نوع المستند (فاتورة بيع، فاتورة شراء، إلخ)
   - RecordID: bigint - رقم العملية (رقم الفاتورة)
   - TheNumber: bigint - الرقم الظاهر في الفاتورة
   - TheDate: datetime - تاريخ إصدار الفاتورة
   - ClientID: bigint - معرف العميل
   - ClientName: varchar(150) - اسم العميل
   - ItemID: bigint - معرف الصنف
   - ItemName: varchar(200) - اسم الصنف
   - ItemNumber: bigint - رقم الصنف
   - CategoryName: varchar(200) - اسم تصنيف الصنف
   - UnitPrice: numeric(18,6) - سعر الوحدة
   - Quantity: numeric(18,6) - الكمية المباعة
   - Amount: numeric(18,6) - المبلغ (الكمية × السعر)
   - StoreID: bigint - معرف المخزن
   - StoreName: varchar(150) - اسم المخزن
   - BranchID: bigint - معرف الفرع
   - BranchName: varchar(150) - اسم الفرع
   - UserName: varchar(150) - اسم المستخدم
   - CurrencyName: varchar(150) - اسم العملة
   - TheMethod: varchar(150) - طريقة الدفع
   - ExpiryDate: date - تاريخ انتهاء الصلاحية

2. tbltemp_Inv_MainInvoice (جدول تفاصيل الفواتير):
   - ID: bigint PRIMARY KEY - المعرف الفريد للسجل
   - DocumentName: varchar(150) - نوع المستند
   - RecordID: bigint - رقم العملية
   - TheNumber: bigint - رقم الفاتورة
   - SupplierName: varchar(150) - اسم المورد
   - TheDate: datetime - تاريخ الفاتورة
   - ItemID: bigint - معرف الصنف
   - UnitPrice: numeric(18,6) - سعر الوحدة
   - Quantity: numeric(18,6) - الكمية
   - TotalAmount: numeric(18,6) - إجمالي المبلغ
   - StoreID: bigint - المخزن
   - BranchID: bigint - الفرع
   - ClientID: bigint - معرف العميل
   - DistributorName: varchar(150) - اسم الموزع
   - CostCenterName: varchar(200) - اسم مركز التكلفة
   - ExpiryDate: date - تاريخ الصلاحية

ملاحظات مهمة:
- الجدولان مؤقتان ويحتويان على بيانات مدمجة (denormalized) لسهولة التحليل
- DocumentName يحدد نوع العملية (بيع، شراء، إرجاع، إلخ)
- يمكن ربط الجدولين عبر RecordID أو ItemID
- الأسعار والكميات بدقة عالية (numeric(18,6))
"""
        
        # Arabic prompt template
        self.arabic_prompt = PromptTemplate(
            input_variables=["query"],
            template=f"""أنت خبير في قواعد البيانات المحاسبية وتحويل الاستعلامات الطبيعية إلى SQL.

{schema_description}

مهامك:
1. تحليل الاستعلام الطبيعي باللغة العربية بعناية
2. تحويله إلى استعلام SQL صحيح ومحسن
3. تقديم شرح واضح للاستعلام باللغة العربية
4. تقدير مستوى الثقة في دقة الاستعلام
5. تحديد الجداول المستخدمة ونوع الاستعلام

قواعد مهمة:
- استخدم أسماء الجداول والأعمدة الصحيحة فقط
- تأكد من صحة العلاقات بين الجداول (Foreign Keys)
- استخدم JOIN عند الحاجة لربط الجداول
- أضف WHERE clauses مناسبة للتصفية
- استخدم GROUP BY و ORDER BY عند الحاجة
- تجنب استعلامات DELETE أو UPDATE إلا إذا طُلب صراحة

{{format_instructions}}

الاستعلام: {{query}}

حول هذا الاستعلام إلى SQL مع الشرح:"""
        )
        
        # English prompt template
        self.english_prompt = PromptTemplate(
            input_variables=["query"],
            template=f"""You are an expert in accounting databases and converting natural language queries to SQL.

Database Schema:
1. tbltemp_ItemsMain (Main Items Movement Table):
   - ID: bigint PRIMARY KEY - Unique record identifier
   - DocumentName: varchar(150) - Document type (sales invoice, purchase invoice, etc.)
   - RecordID: bigint - Operation number (invoice number)
   - TheDate: datetime - Invoice date
   - ClientName: varchar(150) - Customer name
   - ItemName: varchar(200) - Item name
   - CategoryName: varchar(200) - Item category
   - UnitPrice: numeric(18,6) - Unit price
   - Quantity: numeric(18,6) - Quantity sold
   - Amount: numeric(18,6) - Total amount
   - StoreName: varchar(150) - Store name
   - BranchName: varchar(150) - Branch name

2. tbltemp_Inv_MainInvoice (Invoice Details Table):
   - ID: bigint PRIMARY KEY - Unique record identifier
   - DocumentName: varchar(150) - Document type
   - SupplierName: varchar(150) - Supplier name
   - TheDate: datetime - Invoice date
   - UnitPrice: numeric(18,6) - Unit price
   - Quantity: numeric(18,6) - Quantity
   - TotalAmount: numeric(18,6) - Total amount

Your tasks:
1. Carefully analyze the natural language query in English
2. Convert it to a correct and optimized SQL query
3. Provide a clear explanation of the query in English
4. Estimate confidence level in the query accuracy
5. Identify tables used and query type

Important rules:
- Use only correct table and column names from the schema above
- DocumentName field determines operation type (sales, purchase, return, etc.)
- Use LIKE '%بيع%' for sales operations, LIKE '%شراء%' for purchases
- Add appropriate WHERE clauses for filtering
- Use GROUP BY and ORDER BY when needed
- Avoid DELETE or UPDATE queries unless explicitly requested

{{format_instructions}}

Query: {{query}}

Convert this query to SQL with explanation:"""
        )
    
    @timing_decorator
    @cache_llm_response
    def generate_sql_query(self, natural_query: str, language: str = "ar") -> SQLQuery:
        """
        Convert natural language query to SQL using Groq with LangChain patterns
        
        Args:
            natural_query: User's natural language query
            language: Language preference (ar/en)
            
        Returns:
            SQLQuery object with generated SQL and metadata
        """
        try:
            # Select appropriate prompt template
            prompt_template = self.arabic_prompt if language == "ar" else self.english_prompt
            
            # Format the prompt
            formatted_prompt = prompt_template.format(
                query=natural_query,
                format_instructions=self.output_parser.get_format_instructions()
            )
            
            # Call Groq API
            completion = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "user", "content": formatted_prompt}
                ],
                temperature=0.1,  # Low temperature for consistent SQL generation
                max_tokens=1000,
                top_p=0.95
            )
            
            # Get response content
            response_content = completion.choices[0].message.content or ""

            # Parse the response using LangChain output parser
            try:
                parsed_output = self.output_parser.parse(response_content)

                return SQLQuery(
                    sql=parsed_output.sql,
                    explanation=parsed_output.explanation,
                    confidence=float(parsed_output.confidence)
                )
            except Exception as parse_error:
                logger.warning(f"Failed to parse with LangChain parser: {parse_error}")
                # Fallback to manual JSON parsing
                return self._manual_parse_response(response_content, language)
            
        except Exception as e:
            logger.error(f"Error generating SQL query: {str(e)}")
            # Fallback response
            return SQLQuery(
                sql="SELECT 1 as error",
                explanation="خطأ في تحليل الاستعلام" if language == "ar" else "Error parsing query",
                confidence=0.0
            )
    
    def _manual_parse_response(self, response: str, language: str) -> SQLQuery:
        """Manual parsing as fallback when LangChain parser fails"""
        try:
            # Try to extract JSON from response
            if "```json" in response:
                json_start = response.find("```json") + 7
                json_end = response.find("```", json_start)
                json_str = response[json_start:json_end].strip()
            elif "{" in response and "}" in response:
                json_start = response.find("{")
                json_end = response.rfind("}") + 1
                json_str = response[json_start:json_end]
            else:
                raise ValueError("No JSON found in response")
            
            # Parse JSON
            parsed = json.loads(json_str)
            
            return SQLQuery(
                sql=parsed.get("sql", ""),
                explanation=parsed.get("explanation", ""),
                confidence=float(parsed.get("confidence", 0.5))
            )
            
        except Exception as e:
            logger.error(f"Manual parsing also failed: {str(e)}")
            # Final fallback
            return SQLQuery(
                sql="SELECT 1 as error",
                explanation="خطأ في تحليل الاستعلام" if language == "ar" else "Error parsing query",
                confidence=0.0
            )
