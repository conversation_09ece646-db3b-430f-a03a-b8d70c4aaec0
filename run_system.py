#!/usr/bin/env python3
"""
System launcher for the Accounting Agent System
"""
import os
import sys
import time
import subprocess
import threading
import signal
from pathlib import Path

def run_backend():
    """Run FastAPI backend"""
    print("🚀 Starting Backend API...")
    os.chdir("backend")
    try:
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--reload", 
            "--host", "127.0.0.1", 
            "--port", "8000"
        ], check=True)
    except KeyboardInterrupt:
        print("🛑 Backend stopped")
    except Exception as e:
        print(f"❌ Backend error: {e}")

def run_frontend():
    """Run Django frontend"""
    print("🌐 Starting Frontend...")
    os.chdir("frontend")
    
    # Run migrations first
    try:
        print("📦 Running migrations...")
        subprocess.run([sys.executable, "manage.py", "migrate"], check=True)
        print("✅ Migrations completed")
    except Exception as e:
        print(f"⚠️ Migration warning: {e}")
    
    # Start Django server
    try:
        subprocess.run([
            sys.executable, "manage.py", "runserver", 
            "127.0.0.1:8080"
        ], check=True)
    except KeyboardInterrupt:
        print("🛑 Frontend stopped")
    except Exception as e:
        print(f"❌ Frontend error: {e}")

def check_requirements():
    """Check if requirements are installed"""
    try:
        import fastapi
        import django
        import groq
        import langchain
        print("✅ All requirements are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing requirement: {e}")
        print("Please run: pip install -r requirements.txt")
        return False

def main():
    """Main launcher"""
    print("=" * 60)
    print("🤖 نظام الوكيل الذكي للاستعلامات المحاسبية")
    print("   Intelligent Accounting Agent System")
    print("=" * 60)
    
    # Check requirements
    if not check_requirements():
        return
    
    # Create logs directory
    Path("logs").mkdir(exist_ok=True)
    Path("frontend/logs").mkdir(exist_ok=True)
    
    print("\n📋 System Information:")
    print(f"   Backend API: http://127.0.0.1:8000")
    print(f"   Frontend:    http://127.0.0.1:8080")
    print(f"   API Docs:    http://127.0.0.1:8000/docs")
    
    print("\n🔧 Starting services...")
    
    # Start backend in a separate thread
    backend_thread = threading.Thread(target=run_backend, daemon=True)
    backend_thread.start()
    
    # Wait a bit for backend to start
    print("⏳ Waiting for backend to start...")
    time.sleep(3)
    
    # Start frontend in main thread
    try:
        run_frontend()
    except KeyboardInterrupt:
        print("\n🛑 Shutting down system...")
        print("👋 Goodbye!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 System interrupted")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ System error: {e}")
        sys.exit(1)
