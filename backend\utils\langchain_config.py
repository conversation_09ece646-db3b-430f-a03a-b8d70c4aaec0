"""
LangChain configuration and utilities for Groq integration
"""
import logging
from typing import Dict, Any, List
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.pydantic_v1 import BaseModel, Field
from backend.config import settings

logger = logging.getLogger(__name__)

class SQLQueryResponse(BaseModel):
    """Structured response model for SQL query generation"""
    sql: str = Field(description="Generated SQL query")
    explanation: str = Field(description="Explanation of the query in the requested language")
    confidence: float = Field(description="Confidence score between 0.0 and 1.0", ge=0.0, le=1.0)
    tables_used: List[str] = Field(description="List of database tables used in the query")
    query_type: str = Field(description="Type of query (SELECT, INSERT, UPDATE, DELETE, etc.)")

class DatabaseSchema:
    """Real database schema information for prompt context"""

    TABLES = {
        "tbltemp_ItemsMain": {
            "description": "جدول حركات الأصناف الرئيسي - Main items movement table with comprehensive details",
            "columns": {
                "ID": "bigint PRIMARY KEY - المعرف الفريد للسجل",
                "DocumentName": "varchar(150) - نوع المستند (فاتورة بيع، فاتورة شراء، إلخ)",
                "RecordID": "bigint - رقم العملية (رقم الفاتورة)",
                "TheNumber": "bigint - الرقم الظاهر في الفاتورة",
                "TheDate": "datetime - تاريخ إصدار الفاتورة",
                "ClientID": "bigint - معرف العميل",
                "ClientName": "varchar(150) - اسم العميل",
                "ItemID": "bigint - معرف الصنف",
                "ItemName": "varchar(200) - اسم الصنف",
                "ItemNumber": "bigint - رقم الصنف",
                "CategoryName": "varchar(200) - اسم تصنيف الصنف",
                "UnitPrice": "numeric(18,6) - سعر الوحدة",
                "Quantity": "numeric(18,6) - الكمية المباعة",
                "Amount": "numeric(18,6) - المبلغ (الكمية × السعر)",
                "StoreID": "bigint - معرف المخزن",
                "StoreName": "varchar(150) - اسم المخزن",
                "BranchID": "bigint - معرف الفرع",
                "BranchName": "varchar(150) - اسم الفرع",
                "UserName": "varchar(150) - اسم المستخدم",
                "CurrencyName": "varchar(150) - اسم العملة",
                "TheMethod": "varchar(150) - طريقة الدفع",
                "ExpiryDate": "date - تاريخ انتهاء الصلاحية",
                "ISExpiry": "bit - هل له صلاحية؟",
                "Bonus": "numeric(18,6) - الكمية المجانية"
            }
        },
        "tbltemp_Inv_MainInvoice": {
            "description": "جدول تفاصيل الفواتير - Invoice details table with high precision calculations",
            "columns": {
                "ID": "bigint PRIMARY KEY - المعرف الفريد للسجل",
                "DocumentName": "varchar(150) - نوع المستند",
                "RecordID": "bigint - رقم العملية",
                "TheNumber": "bigint - رقم الفاتورة",
                "SupplierName": "varchar(150) - اسم المورد",
                "TheDate": "datetime - تاريخ الفاتورة",
                "ItemID": "bigint - معرف الصنف",
                "UnitPrice": "numeric(18,6) - سعر الوحدة",
                "Quantity": "numeric(18,6) - الكمية",
                "TotalAmount": "numeric(18,6) - إجمالي المبلغ",
                "StoreID": "bigint - المخزن",
                "BranchID": "bigint - الفرع",
                "ClientID": "bigint - معرف العميل",
                "DistributorName": "varchar(150) - اسم الموزع",
                "CostCenterName": "varchar(200) - اسم مركز التكلفة",
                "ExpiryDate": "date - تاريخ الصلاحية",
                "Bonus": "numeric(18,6) - الكمية المجانية",
                "MainUnitQuantity": "numeric(37,12) - الكمية بالوحدة الأساسية",
                "MainUnitPrice": "numeric(38,20) - السعر بالوحدة الأساسية"
            }
        }
    }
    
    @classmethod
    def get_schema_description(cls, language: str = "ar") -> str:
        """Get formatted schema description"""
        if language == "ar":
            schema_text = "هيكل قاعدة البيانات المحاسبية:\n\n"
            for table_name, table_info in cls.TABLES.items():
                schema_text += f"جدول {table_name} ({table_info['description']}):\n"
                for col_name, col_desc in table_info['columns'].items():
                    schema_text += f"  - {col_name}: {col_desc}\n"
                schema_text += "\n"
        else:
            schema_text = "Accounting Database Schema:\n\n"
            for table_name, table_info in cls.TABLES.items():
                schema_text += f"Table {table_name} ({table_info['description']}):\n"
                for col_name, col_desc in table_info['columns'].items():
                    schema_text += f"  - {col_name}: {col_desc}\n"
                schema_text += "\n"
        
        return schema_text

class PromptTemplates:
    """Collection of prompt templates for different languages"""
    
    @staticmethod
    def get_arabic_system_prompt() -> str:
        """Arabic system prompt for SQL generation"""
        return """أنت خبير في قواعد البيانات المحاسبية وتحويل الاستعلامات الطبيعية إلى SQL.

مهامك:
1. تحليل الاستعلام الطبيعي باللغة العربية بعناية
2. تحويله إلى استعلام SQL صحيح ومحسن
3. تقديم شرح واضح للاستعلام باللغة العربية
4. تقدير مستوى الثقة في دقة الاستعلام
5. تحديد الجداول المستخدمة ونوع الاستعلام

قواعد مهمة:
- استخدم أسماء الجداول والأعمدة الصحيحة فقط من الهيكل المحدد
- الجدولان الرئيسيان: tbltemp_ItemsMain و tbltemp_Inv_MainInvoice
- DocumentName يحدد نوع العملية: استخدم LIKE '%بيع%' للمبيعات، LIKE '%شراء%' للمشتريات
- يمكن ربط الجدولين عبر RecordID أو ItemID عند الحاجة
- أضف WHERE clauses مناسبة للتصفية
- استخدم GROUP BY و ORDER BY عند الحاجة
- تجنب استعلامات DELETE أو UPDATE إلا إذا طُلب صراحة
- استخدم TOP للحد من النتائج عند الحاجة

{schema}

{format_instructions}

مثال على الاستجابة:
```json
{{
  "sql": "SELECT TOP 10 ItemName, SUM(Quantity) AS TotalQty FROM tbltemp_ItemsMain WHERE DocumentName LIKE '%بيع%' GROUP BY ItemName ORDER BY TotalQty DESC",
  "explanation": "هذا الاستعلام يجلب أكثر 10 منتجات مبيعاً من حيث الكمية من جدول حركات الأصناف، مع التركيز على عمليات البيع فقط",
  "confidence": 0.95,
  "tables_used": ["tbltemp_ItemsMain"],
  "query_type": "SELECT"
}}
```"""

    @staticmethod
    def get_english_system_prompt() -> str:
        """English system prompt for SQL generation"""
        return """You are an expert in accounting databases and converting natural language queries to SQL.

Your tasks:
1. Carefully analyze the natural language query in English
2. Convert it to a correct and optimized SQL query
3. Provide a clear explanation of the query in English
4. Estimate confidence level in the query accuracy
5. Identify tables used and query type

Important rules:
- Use only correct table and column names from the real schema provided
- Main tables: tbltemp_ItemsMain and tbltemp_Inv_MainInvoice
- DocumentName determines operation type: use LIKE '%بيع%' for sales, LIKE '%شراء%' for purchases
- Tables can be joined via RecordID or ItemID when needed
- Add appropriate WHERE clauses for filtering
- Use GROUP BY and ORDER BY when needed
- Avoid DELETE or UPDATE queries unless explicitly requested
- Use TOP to restrict results when appropriate

{schema}

{format_instructions}

Example response:
```json
{{
  "sql": "SELECT TOP 10 ItemName, SUM(Quantity) AS TotalQty FROM tbltemp_ItemsMain WHERE DocumentName LIKE '%بيع%' GROUP BY ItemName ORDER BY TotalQty DESC",
  "explanation": "This query retrieves the top 10 best-selling products by quantity from the items movement table, focusing only on sales operations",
  "confidence": 0.95,
  "tables_used": ["tbltemp_ItemsMain"],
  "query_type": "SELECT"
}}
```"""

    @classmethod
    def create_prompt_template(cls, language: str = "ar") -> PromptTemplate:
        """Create prompt template for the specified language"""
        parser = PydanticOutputParser(pydantic_object=SQLQueryResponse)
        schema_desc = DatabaseSchema.get_schema_description(language)
        
        if language == "ar":
            system_prompt = cls.get_arabic_system_prompt()
            user_template = "الاستعلام: {query}\n\nحول هذا الاستعلام إلى SQL مع الشرح:"
        else:
            system_prompt = cls.get_english_system_prompt()
            user_template = "Query: {query}\n\nConvert this query to SQL with explanation:"
        
        full_template = system_prompt + "\n\n" + user_template
        
        return PromptTemplate(
            template=full_template,
            input_variables=["query"],
            partial_variables={
                "schema": schema_desc,
                "format_instructions": parser.get_format_instructions()
            }
        )

class LangChainUtils:
    """Utility functions for LangChain operations"""
    
    @staticmethod
    def validate_sql_response(response: Dict[str, Any]) -> bool:
        """Validate SQL response structure"""
        required_fields = ["sql", "explanation", "confidence", "tables_used", "query_type"]
        return all(field in response for field in required_fields)
    
    @staticmethod
    def sanitize_sql(sql: str) -> str:
        """Basic SQL sanitization"""
        # Remove potentially dangerous keywords
        dangerous_keywords = ["DROP", "DELETE", "TRUNCATE", "ALTER", "CREATE", "INSERT", "UPDATE"]
        sql_upper = sql.upper()
        
        for keyword in dangerous_keywords:
            if keyword in sql_upper and not sql_upper.strip().startswith("SELECT"):
                logger.warning(f"Potentially dangerous SQL detected: {keyword}")
                return "SELECT 1 as error -- Dangerous operation blocked"
        
        return sql
    
    @staticmethod
    def extract_table_names(sql: str) -> List[str]:
        """Extract table names from SQL query"""
        import re
        
        # Simple regex to find table names after FROM and JOIN
        pattern = r'(?:FROM|JOIN)\s+(\w+)'
        matches = re.findall(pattern, sql, re.IGNORECASE)
        
        # Filter to only known tables
        known_tables = set(DatabaseSchema.TABLES.keys())
        found_tables = [table.lower() for table in matches if table.lower() in known_tables]
        
        return list(set(found_tables))  # Remove duplicates
