"""
LangChain configuration and utilities for Groq integration
"""
import logging
from typing import Dict, Any, List
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.pydantic_v1 import BaseModel, Field
from backend.config import settings

logger = logging.getLogger(__name__)

class SQLQueryResponse(BaseModel):
    """Structured response model for SQL query generation"""
    sql: str = Field(description="Generated SQL query")
    explanation: str = Field(description="Explanation of the query in the requested language")
    confidence: float = Field(description="Confidence score between 0.0 and 1.0", ge=0.0, le=1.0)
    tables_used: List[str] = Field(description="List of database tables used in the query")
    query_type: str = Field(description="Type of query (SELECT, INSERT, UPDATE, DELETE, etc.)")

class DatabaseSchema:
    """Database schema information for prompt context"""
    
    TABLES = {
        "customers": {
            "description": "Customer information table",
            "columns": {
                "customer_id": "INT PRIMARY KEY - Unique customer identifier",
                "name": "NVARCHAR(100) - Customer full name",
                "email": "NVARCHAR(100) - Customer email address",
                "phone": "NVARCHAR(20) - Customer phone number",
                "address": "NVARCHAR(255) - Customer address",
                "created_date": "DATETIME - Account creation date",
                "status": "NVARCHAR(20) - Customer status (active, inactive)"
            }
        },
        "products": {
            "description": "Product catalog table",
            "columns": {
                "product_id": "INT PRIMARY KEY - Unique product identifier",
                "name": "NVARCHAR(100) - Product name",
                "description": "NVARCHAR(500) - Product description",
                "price": "DECIMAL(10,2) - Product price",
                "category": "NVARCHAR(50) - Product category",
                "stock_quantity": "INT - Available stock quantity",
                "created_date": "DATETIME - Product creation date",
                "status": "NVARCHAR(20) - Product status (active, discontinued)"
            }
        },
        "sales": {
            "description": "Sales transactions table",
            "columns": {
                "sale_id": "INT PRIMARY KEY - Unique sale identifier",
                "customer_id": "INT - Foreign key to customers table",
                "product_id": "INT - Foreign key to products table",
                "quantity": "INT - Quantity sold",
                "unit_price": "DECIMAL(10,2) - Price per unit at time of sale",
                "total_amount": "DECIMAL(10,2) - Total sale amount",
                "sale_date": "DATETIME - Date and time of sale",
                "salesperson": "NVARCHAR(100) - Name of salesperson",
                "status": "NVARCHAR(20) - Sale status (completed, pending, cancelled)"
            }
        },
        "invoices": {
            "description": "Invoice records table",
            "columns": {
                "invoice_id": "INT PRIMARY KEY - Unique invoice identifier",
                "customer_id": "INT - Foreign key to customers table",
                "invoice_number": "NVARCHAR(50) - Human-readable invoice number",
                "invoice_date": "DATETIME - Invoice creation date",
                "due_date": "DATETIME - Payment due date",
                "subtotal": "DECIMAL(10,2) - Subtotal before tax",
                "tax_amount": "DECIMAL(10,2) - Tax amount",
                "total_amount": "DECIMAL(10,2) - Total invoice amount",
                "status": "NVARCHAR(20) - Invoice status (draft, sent, paid, overdue)"
            }
        },
        "payments": {
            "description": "Payment records table",
            "columns": {
                "payment_id": "INT PRIMARY KEY - Unique payment identifier",
                "invoice_id": "INT - Foreign key to invoices table",
                "payment_date": "DATETIME - Date of payment",
                "amount": "DECIMAL(10,2) - Payment amount",
                "payment_method": "NVARCHAR(50) - Payment method (cash, card, transfer, etc.)",
                "reference_number": "NVARCHAR(100) - Payment reference number",
                "status": "NVARCHAR(20) - Payment status (completed, pending, failed)"
            }
        }
    }
    
    @classmethod
    def get_schema_description(cls, language: str = "ar") -> str:
        """Get formatted schema description"""
        if language == "ar":
            schema_text = "هيكل قاعدة البيانات المحاسبية:\n\n"
            for table_name, table_info in cls.TABLES.items():
                schema_text += f"جدول {table_name} ({table_info['description']}):\n"
                for col_name, col_desc in table_info['columns'].items():
                    schema_text += f"  - {col_name}: {col_desc}\n"
                schema_text += "\n"
        else:
            schema_text = "Accounting Database Schema:\n\n"
            for table_name, table_info in cls.TABLES.items():
                schema_text += f"Table {table_name} ({table_info['description']}):\n"
                for col_name, col_desc in table_info['columns'].items():
                    schema_text += f"  - {col_name}: {col_desc}\n"
                schema_text += "\n"
        
        return schema_text

class PromptTemplates:
    """Collection of prompt templates for different languages"""
    
    @staticmethod
    def get_arabic_system_prompt() -> str:
        """Arabic system prompt for SQL generation"""
        return """أنت خبير في قواعد البيانات المحاسبية وتحويل الاستعلامات الطبيعية إلى SQL.

مهامك:
1. تحليل الاستعلام الطبيعي باللغة العربية بعناية
2. تحويله إلى استعلام SQL صحيح ومحسن
3. تقديم شرح واضح للاستعلام باللغة العربية
4. تقدير مستوى الثقة في دقة الاستعلام
5. تحديد الجداول المستخدمة ونوع الاستعلام

قواعد مهمة:
- استخدم أسماء الجداول والأعمدة الصحيحة فقط
- تأكد من صحة العلاقات بين الجداول (Foreign Keys)
- استخدم JOIN عند الحاجة لربط الجداول
- أضف WHERE clauses مناسبة للتصفية
- استخدم GROUP BY و ORDER BY عند الحاجة
- تجنب استعلامات DELETE أو UPDATE إلا إذا طُلب صراحة
- استخدم LIMIT أو TOP للحد من النتائج عند الحاجة

{schema}

{format_instructions}

مثال على الاستجابة:
```json
{{
  "sql": "SELECT c.name, SUM(s.total_amount) as total_sales FROM customers c JOIN sales s ON c.customer_id = s.customer_id WHERE s.sale_date >= '2024-01-01' GROUP BY c.customer_id, c.name ORDER BY total_sales DESC",
  "explanation": "هذا الاستعلام يجلب أسماء العملاء مع إجمالي مبيعاتهم من بداية عام 2024، مرتبة من الأعلى إلى الأقل",
  "confidence": 0.95,
  "tables_used": ["customers", "sales"],
  "query_type": "SELECT"
}}
```"""

    @staticmethod
    def get_english_system_prompt() -> str:
        """English system prompt for SQL generation"""
        return """You are an expert in accounting databases and converting natural language queries to SQL.

Your tasks:
1. Carefully analyze the natural language query in English
2. Convert it to a correct and optimized SQL query
3. Provide a clear explanation of the query in English
4. Estimate confidence level in the query accuracy
5. Identify tables used and query type

Important rules:
- Use only correct table and column names from the schema
- Ensure proper relationships between tables (Foreign Keys)
- Use JOIN when needed to connect tables
- Add appropriate WHERE clauses for filtering
- Use GROUP BY and ORDER BY when needed
- Avoid DELETE or UPDATE queries unless explicitly requested
- Use LIMIT or TOP to restrict results when appropriate

{schema}

{format_instructions}

Example response:
```json
{{
  "sql": "SELECT c.name, SUM(s.total_amount) as total_sales FROM customers c JOIN sales s ON c.customer_id = s.customer_id WHERE s.sale_date >= '2024-01-01' GROUP BY c.customer_id, c.name ORDER BY total_sales DESC",
  "explanation": "This query retrieves customer names with their total sales from the beginning of 2024, ordered from highest to lowest",
  "confidence": 0.95,
  "tables_used": ["customers", "sales"],
  "query_type": "SELECT"
}}
```"""

    @classmethod
    def create_prompt_template(cls, language: str = "ar") -> PromptTemplate:
        """Create prompt template for the specified language"""
        parser = PydanticOutputParser(pydantic_object=SQLQueryResponse)
        schema_desc = DatabaseSchema.get_schema_description(language)
        
        if language == "ar":
            system_prompt = cls.get_arabic_system_prompt()
            user_template = "الاستعلام: {query}\n\nحول هذا الاستعلام إلى SQL مع الشرح:"
        else:
            system_prompt = cls.get_english_system_prompt()
            user_template = "Query: {query}\n\nConvert this query to SQL with explanation:"
        
        full_template = system_prompt + "\n\n" + user_template
        
        return PromptTemplate(
            template=full_template,
            input_variables=["query"],
            partial_variables={
                "schema": schema_desc,
                "format_instructions": parser.get_format_instructions()
            }
        )

class LangChainUtils:
    """Utility functions for LangChain operations"""
    
    @staticmethod
    def validate_sql_response(response: Dict[str, Any]) -> bool:
        """Validate SQL response structure"""
        required_fields = ["sql", "explanation", "confidence", "tables_used", "query_type"]
        return all(field in response for field in required_fields)
    
    @staticmethod
    def sanitize_sql(sql: str) -> str:
        """Basic SQL sanitization"""
        # Remove potentially dangerous keywords
        dangerous_keywords = ["DROP", "DELETE", "TRUNCATE", "ALTER", "CREATE", "INSERT", "UPDATE"]
        sql_upper = sql.upper()
        
        for keyword in dangerous_keywords:
            if keyword in sql_upper and not sql_upper.strip().startswith("SELECT"):
                logger.warning(f"Potentially dangerous SQL detected: {keyword}")
                return "SELECT 1 as error -- Dangerous operation blocked"
        
        return sql
    
    @staticmethod
    def extract_table_names(sql: str) -> List[str]:
        """Extract table names from SQL query"""
        import re
        
        # Simple regex to find table names after FROM and JOIN
        pattern = r'(?:FROM|JOIN)\s+(\w+)'
        matches = re.findall(pattern, sql, re.IGNORECASE)
        
        # Filter to only known tables
        known_tables = set(DatabaseSchema.TABLES.keys())
        found_tables = [table.lower() for table in matches if table.lower() in known_tables]
        
        return list(set(found_tables))  # Remove duplicates
