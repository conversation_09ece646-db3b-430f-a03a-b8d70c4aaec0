"""
FastAPI main application for the Accounting Agent System
"""
import logging
import uuid
from datetime import datetime
from fastapi import FastAP<PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse
from contextlib import asynccontextmanager

from backend.config import settings
from backend.models.schemas import (
    QueryRequest, AgentResponse, ErrorResponse, HealthCheck, SessionInfo
)
from backend.services.llm_service import LLMService
from backend.services.database_service import DatabaseService
from backend.services.analysis_service import AnalysisService
from backend.services.session_service import SessionService

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(settings.LOG_FILE),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Global service instances
llm_service = None
database_service = None
analysis_service = None
session_service = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global llm_service, database_service, analysis_service, session_service
    
    # Startup
    logger.info("Starting Accounting Agent System...")
    try:
        llm_service = LLMService()
        database_service = DatabaseService()
        analysis_service = AnalysisService()
        session_service = SessionService()
        logger.info("All services initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize services: {str(e)}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down Accounting Agent System...")
    if database_service:
        await database_service.close()

# Create FastAPI app
app = FastAPI(
    title="نظام الوكيل الذكي للاستعلامات المحاسبية",
    description="Intelligent Agent System for Accounting Queries",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def get_services():
    """Dependency to get service instances"""
    return {
        "llm": llm_service,
        "database": database_service,
        "analysis": analysis_service,
        "session": session_service
    }

@app.get("/", response_model=dict)
async def root():
    """Root endpoint"""
    return {
        "message": "مرحباً بك في نظام الوكيل الذكي للاستعلامات المحاسبية",
        "message_en": "Welcome to the Intelligent Accounting Agent System",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.get("/health", response_model=HealthCheck)
async def health_check(services: dict = Depends(get_services)):
    """Health check endpoint"""
    try:
        # Check database connection
        db_connected = await services["database"].check_connection()
        
        # Check LLM availability
        llm_available = services["llm"] is not None
        
        return HealthCheck(
            status="healthy" if db_connected and llm_available else "unhealthy",
            database_connected=db_connected,
            llm_available=llm_available
        )
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return HealthCheck(
            status="unhealthy",
            database_connected=False,
            llm_available=False
        )

@app.post("/query", response_model=AgentResponse)
async def process_query(
    request: QueryRequest,
    services: dict = Depends(get_services)
):
    """
    Process natural language query and return analysis
    """
    try:
        # Generate or use existing session ID
        session_id = request.session_id or str(uuid.uuid4())
        
        logger.info(f"Processing query: {request.query[:100]}... (Session: {session_id})")
        
        # Step 1: Convert natural language to SQL using LLM
        sql_query = services["llm"].generate_sql_query(
            request.query, 
            request.language
        )
        
        # Step 2: Execute SQL query
        query_result = await services["database"].execute_query(sql_query.sql)
        
        # Step 3: Analyze results and generate insights
        analysis = services["analysis"].analyze_results(
            query_result, 
            request.query, 
            request.language
        )
        
        # Step 4: Save to session
        response = AgentResponse(
            original_query=request.query,
            sql_query=sql_query,
            query_result=query_result,
            analysis=analysis,
            session_id=session_id
        )
        
        services["session"].save_interaction(session_id, response)
        
        logger.info(f"Query processed successfully (Session: {session_id})")
        return response
        
    except Exception as e:
        logger.error(f"Error processing query: {str(e)}")
        error_response = ErrorResponse(
            error=str(e),
            error_type=type(e).__name__,
            session_id=session_id if 'session_id' in locals() else None
        )
        raise HTTPException(status_code=500, detail=error_response.dict())

@app.get("/sessions/{session_id}", response_model=SessionInfo)
async def get_session_info(
    session_id: str,
    services: dict = Depends(get_services)
):
    """Get session information"""
    try:
        session_info = services["session"].get_session_info(session_id)
        if not session_info:
            raise HTTPException(status_code=404, detail="Session not found")
        return session_info
    except Exception as e:
        logger.error(f"Error getting session info: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/sessions/{session_id}/history")
async def get_session_history(
    session_id: str,
    services: dict = Depends(get_services)
):
    """Get session conversation history"""
    try:
        history = services["session"].get_session_history(session_id)
        return {"session_id": session_id, "history": history}
    except Exception as e:
        logger.error(f"Error getting session history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=settings.FASTAPI_HOST,
        port=settings.FASTAPI_PORT,
        reload=True
    )
