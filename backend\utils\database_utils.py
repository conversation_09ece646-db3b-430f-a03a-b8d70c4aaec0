"""
Database utilities and helper functions
"""
import logging
import pyodbc
from typing import List, Dict, Any, Optional
from backend.config import settings

logger = logging.getLogger(__name__)

class DatabaseUtils:
    """Utility functions for database operations"""
    
    @staticmethod
    def test_connection() -> bool:
        """Test database connection"""
        try:
            connection_string = (
                f"DRIVER={{{settings.DB_DRIVER}}};"
                f"SERVER={settings.DB_SERVER};"
                f"DATABASE={settings.DB_NAME};"
                f"UID={settings.DB_USERNAME};"
                f"PWD={settings.DB_PASSWORD};"
                "TrustServerCertificate=yes;"
            )
            
            connection = pyodbc.connect(connection_string)
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            connection.close()
            
            logger.info("Database connection test successful")
            return True
            
        except Exception as e:
            logger.error(f"Database connection test failed: {str(e)}")
            return False
    
    @staticmethod
    def get_table_info() -> Dict[str, Any]:
        """Get information about all tables in the database"""
        try:
            connection_string = (
                f"DRIVER={{{settings.DB_DRIVER}}};"
                f"SERVER={settings.DB_SERVER};"
                f"DATABASE={settings.DB_NAME};"
                f"UID={settings.DB_USERNAME};"
                f"PWD={settings.DB_PASSWORD};"
                "TrustServerCertificate=yes;"
            )
            
            connection = pyodbc.connect(connection_string)
            cursor = connection.cursor()
            
            # Get table names
            cursor.execute("""
                SELECT TABLE_NAME 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_TYPE = 'BASE TABLE' 
                AND TABLE_SCHEMA = 'dbo'
                ORDER BY TABLE_NAME
            """)
            
            tables = [row[0] for row in cursor.fetchall()]
            
            table_info = {}
            
            # Get column information for each table
            for table in tables:
                cursor.execute("""
                    SELECT 
                        COLUMN_NAME,
                        DATA_TYPE,
                        IS_NULLABLE,
                        COLUMN_DEFAULT,
                        CHARACTER_MAXIMUM_LENGTH
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = ? 
                    AND TABLE_SCHEMA = 'dbo'
                    ORDER BY ORDINAL_POSITION
                """, table)
                
                columns = []
                for row in cursor.fetchall():
                    columns.append({
                        'name': row[0],
                        'type': row[1],
                        'nullable': row[2] == 'YES',
                        'default': row[3],
                        'max_length': row[4]
                    })
                
                # Get row count
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                row_count = cursor.fetchone()[0]
                
                table_info[table] = {
                    'columns': columns,
                    'row_count': row_count
                }
            
            cursor.close()
            connection.close()
            
            return table_info
            
        except Exception as e:
            logger.error(f"Failed to get table info: {str(e)}")
            return {}
    
    @staticmethod
    def execute_setup_script(script_path: str) -> bool:
        """Execute database setup script"""
        try:
            with open(script_path, 'r', encoding='utf-8') as file:
                script_content = file.read()
            
            connection_string = (
                f"DRIVER={{{settings.DB_DRIVER}}};"
                f"SERVER={settings.DB_SERVER};"
                f"DATABASE={settings.DB_NAME};"
                f"UID={settings.DB_USERNAME};"
                f"PWD={settings.DB_PASSWORD};"
                "TrustServerCertificate=yes;"
            )
            
            connection = pyodbc.connect(connection_string)
            cursor = connection.cursor()
            
            # Split script into individual statements
            statements = script_content.split(';')
            
            for statement in statements:
                statement = statement.strip()
                if statement and not statement.startswith('--'):
                    try:
                        cursor.execute(statement)
                        connection.commit()
                    except Exception as e:
                        logger.warning(f"Statement failed (might be expected): {str(e)}")
                        connection.rollback()
            
            cursor.close()
            connection.close()
            
            logger.info("Database setup script executed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to execute setup script: {str(e)}")
            return False
    
    @staticmethod
    def validate_sql_query(sql: str) -> Dict[str, Any]:
        """Validate SQL query without executing it"""
        try:
            connection_string = (
                f"DRIVER={{{settings.DB_DRIVER}}};"
                f"SERVER={settings.DB_SERVER};"
                f"DATABASE={settings.DB_NAME};"
                f"UID={settings.DB_USERNAME};"
                f"PWD={settings.DB_PASSWORD};"
                "TrustServerCertificate=yes;"
            )
            
            connection = pyodbc.connect(connection_string)
            cursor = connection.cursor()
            
            # Use SET NOEXEC ON to validate without executing
            cursor.execute("SET NOEXEC ON")
            cursor.execute(sql)
            cursor.execute("SET NOEXEC OFF")
            
            cursor.close()
            connection.close()
            
            return {
                'valid': True,
                'error': None
            }
            
        except Exception as e:
            return {
                'valid': False,
                'error': str(e)
            }
    
    @staticmethod
    def get_sample_queries() -> List[Dict[str, str]]:
        """Get sample queries for testing"""
        return [
            {
                'arabic': 'ما هو أكثر منتج مبيعاً؟',
                'english': 'What is the best-selling product?',
                'sql': '''
                    SELECT TOP 1 
                        p.name as product_name,
                        SUM(s.quantity) as total_sold,
                        SUM(s.total_amount) as total_revenue
                    FROM products p
                    JOIN sales s ON p.product_id = s.product_id
                    GROUP BY p.product_id, p.name
                    ORDER BY total_sold DESC
                '''
            },
            {
                'arabic': 'ما إجمالي المبيعات هذا الشهر؟',
                'english': 'What are the total sales this month?',
                'sql': '''
                    SELECT 
                        COUNT(*) as total_transactions,
                        SUM(total_amount) as total_revenue
                    FROM sales
                    WHERE MONTH(sale_date) = MONTH(GETDATE())
                    AND YEAR(sale_date) = YEAR(GETDATE())
                '''
            },
            {
                'arabic': 'من هو العميل الأكثر شراءً؟',
                'english': 'Who is the top customer by purchases?',
                'sql': '''
                    SELECT TOP 1
                        c.name as customer_name,
                        COUNT(s.sale_id) as total_orders,
                        SUM(s.total_amount) as total_spent
                    FROM customers c
                    JOIN sales s ON c.customer_id = s.customer_id
                    GROUP BY c.customer_id, c.name
                    ORDER BY total_spent DESC
                '''
            },
            {
                'arabic': 'ما هي المنتجات التي تحتاج إعادة تخزين؟',
                'english': 'Which products need restocking?',
                'sql': '''
                    SELECT 
                        name as product_name,
                        stock_quantity,
                        category
                    FROM products
                    WHERE stock_quantity < 20
                    ORDER BY stock_quantity ASC
                '''
            },
            {
                'arabic': 'ما هي الفواتير المتأخرة؟',
                'english': 'What are the overdue invoices?',
                'sql': '''
                    SELECT 
                        i.invoice_number,
                        c.name as customer_name,
                        i.total_amount,
                        i.due_date,
                        DATEDIFF(day, i.due_date, GETDATE()) as days_overdue
                    FROM invoices i
                    JOIN customers c ON i.customer_id = c.customer_id
                    WHERE i.due_date < GETDATE() 
                    AND i.status != 'paid'
                    ORDER BY days_overdue DESC
                '''
            }
        ]

class DatabaseInitializer:
    """Initialize database with sample data"""
    
    @staticmethod
    def setup_database():
        """Setup database with tables and sample data"""
        try:
            script_path = "database/setup_database.sql"
            success = DatabaseUtils.execute_setup_script(script_path)
            
            if success:
                logger.info("Database initialized successfully")
                return True
            else:
                logger.error("Database initialization failed")
                return False
                
        except Exception as e:
            logger.error(f"Database initialization error: {str(e)}")
            return False
    
    @staticmethod
    def verify_setup():
        """Verify database setup"""
        try:
            table_info = DatabaseUtils.get_table_info()
            
            required_tables = ['customers', 'products', 'sales', 'invoices', 'payments']
            missing_tables = []
            
            for table in required_tables:
                if table not in table_info:
                    missing_tables.append(table)
                else:
                    logger.info(f"Table {table}: {table_info[table]['row_count']} rows")
            
            if missing_tables:
                logger.error(f"Missing tables: {missing_tables}")
                return False
            
            logger.info("Database verification successful")
            return True
            
        except Exception as e:
            logger.error(f"Database verification failed: {str(e)}")
            return False
