"""
Database service for SQL Server connection and query execution
"""
import logging
import time
from typing import List, Dict, Any
import pyodbc
import pandas as pd
from backend.config import settings
from backend.models.schemas import QueryResult
from backend.utils.performance_optimizer import (
    timing_decorator, cache_sql_result, optimize_database_query, performance_monitor
)

logger = logging.getLogger(__name__)

class DatabaseService:
    """Service for database operations"""
    
    def __init__(self):
        self.connection_string = self._build_connection_string()
        self.connection = None
        
    def _build_connection_string(self) -> str:
        """Build SQL Server connection string"""
        return (
            f"DRIVER={{{settings.DB_DRIVER}}};"
            f"SERVER={settings.DB_SERVER},{settings.DB_PORT};"
            f"DATABASE={settings.DB_DATABASE};"
            f"UID={settings.DB_USERNAME};"
            f"PWD={settings.DB_PASSWORD};"
            f"TrustServerCertificate={settings.DB_TRUST_CERTIFICATE};"
        )
    
    async def connect(self):
        """Establish database connection"""
        try:
            if not self.connection:
                self.connection = pyodbc.connect(self.connection_string)
                logger.info("Database connection established")
        except Exception as e:
            logger.error(f"Failed to connect to database: {str(e)}")
            raise
    
    async def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            self.connection = None
            logger.info("Database connection closed")
    
    async def check_connection(self) -> bool:
        """Check if database connection is healthy"""
        try:
            await self.connect()
            cursor = self.connection.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            return True
        except Exception as e:
            logger.error(f"Database connection check failed: {str(e)}")
            return False
    
    @timing_decorator
    @cache_sql_result
    async def execute_query(self, sql: str) -> QueryResult:
        """
        Execute SQL query and return results

        Args:
            sql: SQL query string

        Returns:
            QueryResult object with data and metadata
        """
        start_time = time.time()
        success = False

        try:
            await self.connect()

            # Optimize SQL query
            optimized_sql = optimize_database_query(sql)

            # Use pandas for easier data handling
            df = pd.read_sql(optimized_sql, self.connection)

            # Convert to list of dictionaries
            data = df.to_dict('records')
            columns = df.columns.tolist()
            row_count = len(df)

            execution_time = time.time() - start_time
            success = True

            logger.info(f"Query executed successfully: {row_count} rows in {execution_time:.2f}s")

            result = QueryResult(
                data=data,
                columns=columns,
                row_count=row_count,
                execution_time=execution_time
            )

            # Record performance metrics
            performance_monitor.record_query(success, execution_time)

            return result

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Query execution failed: {str(e)}")

            # Record performance metrics
            performance_monitor.record_query(success, execution_time)

            # Return error result
            return QueryResult(
                data=[{"error": str(e)}],
                columns=["error"],
                row_count=0,
                execution_time=execution_time
            )
    
    async def get_table_schema(self, table_name: str = None) -> Dict[str, Any]:
        """
        Get database schema information
        
        Args:
            table_name: Specific table name (optional)
            
        Returns:
            Dictionary with schema information
        """
        try:
            await self.connect()
            
            if table_name:
                # Get specific table schema
                query = """
                SELECT 
                    COLUMN_NAME,
                    DATA_TYPE,
                    IS_NULLABLE,
                    COLUMN_DEFAULT
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = ?
                ORDER BY ORDINAL_POSITION
                """
                df = pd.read_sql(query, self.connection, params=[table_name])
            else:
                # Get all tables schema
                query = """
                SELECT 
                    TABLE_NAME,
                    COLUMN_NAME,
                    DATA_TYPE,
                    IS_NULLABLE,
                    COLUMN_DEFAULT
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = 'dbo'
                ORDER BY TABLE_NAME, ORDINAL_POSITION
                """
                df = pd.read_sql(query, self.connection)
            
            return df.to_dict('records')
            
        except Exception as e:
            logger.error(f"Failed to get schema: {str(e)}")
            return {}
    
    async def get_sample_data(self, table_name: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Get sample data from a table
        
        Args:
            table_name: Table name
            limit: Number of rows to return
            
        Returns:
            List of sample records
        """
        try:
            await self.connect()
            
            query = f"SELECT TOP {limit} * FROM {table_name}"
            df = pd.read_sql(query, self.connection)
            
            return df.to_dict('records')
            
        except Exception as e:
            logger.error(f"Failed to get sample data: {str(e)}")
            return []
    
    async def validate_sql(self, sql: str) -> bool:
        """
        Validate SQL query without executing it
        
        Args:
            sql: SQL query string
            
        Returns:
            True if valid, False otherwise
        """
        try:
            await self.connect()
            cursor = self.connection.cursor()
            
            # Use SET NOEXEC ON to validate without executing
            cursor.execute("SET NOEXEC ON")
            cursor.execute(sql)
            cursor.execute("SET NOEXEC OFF")
            cursor.close()
            
            return True
            
        except Exception as e:
            logger.error(f"SQL validation failed: {str(e)}")
            return False
