#!/usr/bin/env python3
"""
Quick system test runner
"""
import sys
import os
from pathlib import Path

# Add paths
sys.path.append(str(Path(__file__).parent / "backend"))
sys.path.append(str(Path(__file__).parent / "tests"))

from tests.test_system import run_manual_tests

if __name__ == "__main__":
    print("🚀 نظام الوكيل الذكي للاستعلامات المحاسبية")
    print("   Quick System Test")
    print("=" * 60)
    
    run_manual_tests()
    
    print("\n💡 لاختبار قاعدة البيانات:")
    print("   python test_database_connection.py")
    print("\n🚀 لتشغيل النظام الكامل:")
    print("   python start_system.py")
    print("\n📚 لعرض الوثائق:")
    print("   http://127.0.0.1:8000/docs")
    print("\n🌐 للوصول للواجهة:")
    print("   http://127.0.0.1:8080")
