{% extends 'base.html' %}
{% load static %}

{% block title %}المحادثة الذكية - {{ block.super }}{% endblock %}

{% block content %}
<div class="row h-100">
    <!-- Chat History Sidebar -->
    <div class="col-md-3 d-none d-md-block">
        <div class="card h-100">
            <div class="card-header bg-secondary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    تاريخ المحادثات
                </h6>
            </div>
            <div class="card-body p-0">
                <div id="chat-history" class="list-group list-group-flush" style="max-height: 500px; overflow-y: auto;">
                    {% for query in recent_queries %}
                    <div class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">{{ query.query_text|truncatechars:30 }}</h6>
                            <small>{{ query.created_at|timesince }}</small>
                        </div>
                        <p class="mb-1 text-muted small">{{ query.result_count }} نتيجة</p>
                        <small class="text-success">{{ query.confidence|floatformat:2 }}% ثقة</small>
                    </div>
                    {% empty %}
                    <div class="p-3 text-center text-muted">
                        <i class="fas fa-comments fa-2x mb-2"></i>
                        <p>لا توجد محادثات سابقة</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Main Chat Area -->
    <div class="col-md-9">
        <div class="card h-100">
            <!-- Chat Messages -->
            <div class="card-body" style="height: 60vh; overflow-y: auto;" id="chat-messages">
                <div class="text-center text-muted py-5">
                    <i class="fas fa-robot fa-3x mb-3"></i>
                    <h4>مرحباً بك في النظام الذكي للاستعلامات المحاسبية</h4>
                    <p>اكتب استعلامك بلغة طبيعية وسأساعدك في الحصول على النتائج</p>
                    
                    <!-- Quick Examples -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-body text-start">
                                    <h6 class="card-title text-primary">
                                        <i class="fas fa-chart-bar me-2"></i>
                                        أمثلة سريعة
                                    </h6>
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-outline-primary btn-sm text-start" onclick="setQuery('ما هو أكثر منتج مبيعاً؟')">
                                            ما هو أكثر منتج مبيعاً؟
                                        </button>
                                        <button class="btn btn-outline-primary btn-sm text-start" onclick="setQuery('ما إجمالي المبيعات هذا الشهر؟')">
                                            ما إجمالي المبيعات هذا الشهر؟
                                        </button>
                                        <button class="btn btn-outline-primary btn-sm text-start" onclick="setQuery('من هو العميل الأكثر شراءً؟')">
                                            من هو العميل الأكثر شراءً؟
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-body text-start">
                                    <h6 class="card-title text-success">
                                        <i class="fas fa-language me-2"></i>
                                        English Examples
                                    </h6>
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-outline-success btn-sm text-start" onclick="setQuery('What are the top selling products?')">
                                            What are the top selling products?
                                        </button>
                                        <button class="btn btn-outline-success btn-sm text-start" onclick="setQuery('Show me monthly sales trends')">
                                            Show me monthly sales trends
                                        </button>
                                        <button class="btn btn-outline-success btn-sm text-start" onclick="setQuery('Which customers need attention?')">
                                            Which customers need attention?
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat Input -->
            <div class="card-footer">
                <form id="chat-form" class="d-flex gap-2">
                    <div class="flex-grow-1">
                        <div class="input-group">
                            <input type="text" 
                                   class="form-control" 
                                   id="query-input" 
                                   placeholder="اكتب استعلامك هنا..."
                                   autocomplete="off">
                            <select class="form-select" id="language-select" style="max-width: 100px;">
                                <option value="ar">عربي</option>
                                <option value="en">English</option>
                            </select>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary" id="send-btn">
                        <i class="fas fa-paper-plane"></i>
                        إرسال
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <h5>جاري معالجة استعلامك...</h5>
                <p class="text-muted mb-0">قد يستغرق هذا بضع ثوانٍ</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'chat/js/chat.js' %}"></script>
<script>
    // Initialize chat with session ID
    const sessionId = '{{ session_id }}';
    
    function setQuery(query) {
        document.getElementById('query-input').value = query;
        document.getElementById('query-input').focus();
    }
</script>
{% endblock %}
