{"purpose": "مساعدة الوكيل الذكي على اختيار الجدول المناسب (tbltemp_ItemsMain أو tbltemp_Inv_MainInvoice) بناءً على نوع الاستعلام أو التحليل المطلوب.", "decision_logic": {"general_guidance": "رغم تشابه الجدولين، فإن لكل منهما سياق استخدام مفضل حسب التركيز: تحليل شامل للحركة أم تفاصيل دقيقة للفاتورة؟", "tables_comparison": {"tbltemp_ItemsMain": {"primary_role": "جدول تحليل شامل ومحاسبي", "best_for": ["تحليل المبيعات والمشتريات حسب العميل أو المورد", "تقارير العملاء (أكثر العملاء شراءً)", "تقارير المنتجات (أكثر المنتجات مبيعًا)", "التحليل حسب الفرع، المستخدم، أو المخزن", "الربط مع الحسابات المحاسبية ومراكز التكلفة", "تتبع الصلاحيات وإعادة الطلب", "التحليل المحاسبي (الحسابات، المراكز، العملات)"], "strengths": ["يحتوي على معلومات واسعة: عميل، فرع، مستخدم، حساب، مركز تكلفة", "مناسب للتحليلات الإدارية والاستراتيجية", "يحتوي على حقول مثل ISActive, ReorderPoint, ExpiryDate → مفيد للإدارة اللوجستية", "يُستخدم عند الحاجة لفهم 'من، أين، متى، ولماذا'"], "when_to_choose": "عند طلب استعلام يشمل: العملاء، الفروع، الصلاحيات، المخزون، أو التحليل الشامل للحركة."}, "tbltemp_Inv_MainInvoice": {"primary_role": "جدول دقيق لتفاصيل الفواتير", "best_for": ["تحليل كميات ووحدات القياس (MainUnitQuantity)", "الحسابات الدقيقة بالعملات (MCAmount, TotalAmountByCurrencyInvetory)", "مقارنة الأسعار بين الوحدات باستخدام ExchangeFactor", "استخراج تفاصيل فاتورة معينة بدقة عالية", "التحليل المالي الدقيق (خاصة بالمخزون أو العملات)"], "strengths": ["دقة رياضية عالية (حتى 20 خانة عشرية في MainUnitPrice)", "مخصص لحسابات التحويل بين الوحدات (MainUnit, ExchangeFactor)", "مناسب للحسابات المالية والمخزنية الدقيقة", "يُستخدم عند الحاجة لفهم 'كم، بأي وحدة، وبأي سعر دقيق'"], "when_to_choose": "عند طلب استعلام يركز على: الدقة الحسابية، وحدات القياس، أو تفاصيل فاتورة معينة."}}, "decision_flow": [{"question": "هل يطلب المستخدم تحليلًا يشمل العملاء، الفروع، أو المستخدمين؟", "answer_if_yes": "استخدم tbltemp_ItemsMain", "example_queries": ["من هو أكثر عميل شراءً؟", "ما إجمالي المبيعات حسب الفرع؟", "ما اسم المستخدم الذي سجّل أكثر فواتير؟"]}, {"question": "هل يطلب المستخدم تحليلًا للمنتج (أكثر منتج مبيعًا، متوسط السعر)؟", "answer_if_yes": "استخدم tbltemp_ItemsMain (لأنه يحتوي ItemName, ClientName, BranchName مباشرة)", "note": "رغم أن الجدول الثاني يحتوي على ItemID، لكن tbltemp_ItemsMain أكثر شمولاً في السياقات التسويقية والإدارية."}, {"question": "هل يطلب المستخدم تفاصيل دقيقة للفاتورة أو حسابات مخزنية دقيقة جدًا؟", "answer_if_yes": "استخدم tbltemp_Inv_MainInvoice", "example_queries": ["ما الكمية بالوحدة الأساسية لهذا الصنف؟", "ما السعر الدقيق بالعملة الخاصة بالمخزون؟", "ما معامل التحويل بين الوحدات في هذه الفاتورة؟"]}, {"question": "هل يطلب المستخدم مقارنة بين الوحدات (مثل: كم قطعة في الكرتون)؟", "answer_if_yes": "استخدم tbltemp_Inv_MainInvoice (لوجود MainUnitQuantity, ExchangeFactor)", "note": "(tbltemp_ItemsMain يحتوي ExchangeFactor أيضًا، لكن الجدول الثاني أكثر تخصصًا في هذا السياق)"}, {"question": "هل يطلب المستخدم تحليل الصلاحيات أو إعادة الطلب؟", "answer_if_yes": "استخدم tbltemp_ItemsMain (لوجود حقول مثل ISExpiry, ExpiryDate, ReorderPoint)", "note": "الجدول الثاني لا يحتوي هذه الحقول."}, {"question": "هل يطلب المستخدم معلومات محاسبية (مراكز تكلفة، حسابات)؟", "answer_if_yes": "استخدم tbltemp_ItemsMain", "note": "يحتوي على AccountID, CostCenterID, AccountName"}], "summary_table": [{"use_case": "تحليل العملاء", "recommended_table": "tbltemp_ItemsMain", "reason": "يحتوي على ClientName, ClientID, Amount, TheDate"}, {"use_case": "تحليل المنتجات", "recommended_table": "tbltemp_ItemsMain", "reason": "يحتوي على ItemName, Quantity, UnitPrice, StoreName"}, {"use_case": "الحسابات الدقيقة بالعملة", "recommended_table": "tbltemp_Inv_MainInvoice", "reason": "يحتوي ع<PERSON>ى <PERSON> (دق<PERSON> 38,13)"}, {"use_case": "وحدة القياس الأساسية", "recommended_table": "tbltemp_Inv_MainInvoice", "reason": "يحتوي على MainUnitQuantity, MainUnitPrice, MainUnitID"}, {"use_case": "الصلاحية وإعادة الطلب", "recommended_table": "tbltemp_ItemsMain", "reason": "يحتوي على ExpiryDate, ReorderPoint, ISExpiry"}, {"use_case": "المحاسبة (مراكز التكلفة، الحسابات)", "recommended_table": "tbltemp_ItemsMain", "reason": "يحتوي على CostCenterName, AccountName"}, {"use_case": "تفاصيل فاتورة واحدة بدقة", "recommended_table": "tbltemp_Inv_MainInvoice", "reason": "مصمم خصيصًا لتفاصيل الفواتير"}], "final_note": "إذا لم يكن هناك فرق كبير، يُفضّل استخدام tbltemp_ItemsMain لأنه أكثر شمولاً. أما إذا كانت الدقة الحسابية أو وحدات القياس هي المحور، فاستخدم tbltemp_Inv_MainInvoice."}}