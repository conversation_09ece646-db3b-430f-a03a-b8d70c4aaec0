#!/usr/bin/env python3
"""
Database setup script for the Accounting Agent System
"""
import os
import sys
import logging
from dotenv import load_dotenv

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.utils.database_utils import DatabaseUtils, DatabaseInitializer

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main setup function"""
    print("🚀 إعداد قاعدة البيانات المحاسبية")
    print("=" * 50)
    
    # Test connection first
    print("1. اختبار الاتصال بقاعدة البيانات...")
    if not DatabaseUtils.test_connection():
        print("❌ فشل الاتصال بقاعدة البيانات!")
        print("تأكد من:")
        print("- تشغي<PERSON> SQL Server")
        print("- صحة بيانات الاتصال في ملف .env")
        print("- وجود قاعدة البيانات")
        return False
    
    print("✅ تم الاتصال بقاعدة البيانات بنجاح")
    
    # Setup database
    print("\n2. إعداد الجداول والبيانات التجريبية...")
    if not DatabaseInitializer.setup_database():
        print("❌ فشل في إعداد قاعدة البيانات!")
        return False
    
    print("✅ تم إعداد قاعدة البيانات بنجاح")
    
    # Verify setup
    print("\n3. التحقق من الإعداد...")
    if not DatabaseInitializer.verify_setup():
        print("❌ فشل في التحقق من الإعداد!")
        return False
    
    print("✅ تم التحقق من الإعداد بنجاح")
    
    # Show table information
    print("\n4. معلومات الجداول:")
    table_info = DatabaseUtils.get_table_info()
    for table_name, info in table_info.items():
        print(f"   📊 {table_name}: {info['row_count']} صف")
    
    # Show sample queries
    print("\n5. استعلامات تجريبية:")
    sample_queries = DatabaseUtils.get_sample_queries()
    for i, query in enumerate(sample_queries[:3], 1):
        print(f"   {i}. {query['arabic']}")
    
    print("\n🎉 تم إعداد قاعدة البيانات بنجاح!")
    print("يمكنك الآن تشغيل النظام باستخدام:")
    print("python -m uvicorn backend.main:app --reload")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ تم إلغاء العملية")
        sys.exit(1)
    except Exception as e:
        logger.error(f"خطأ غير متوقع: {str(e)}")
        sys.exit(1)
