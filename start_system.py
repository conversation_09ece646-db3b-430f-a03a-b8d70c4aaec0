#!/usr/bin/env python3
"""
Enhanced system launcher with health checks and monitoring
"""
import os
import sys
import time
import subprocess
import threading
import requests
import signal
from pathlib import Path
from datetime import datetime

class SystemLauncher:
    """Enhanced system launcher with monitoring"""
    
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.backend_url = "http://127.0.0.1:8000"
        self.frontend_url = "http://127.0.0.1:8080"
        self.running = True
        
    def check_requirements(self):
        """Check if all requirements are installed"""
        print("🔍 Checking requirements...")
        
        required_packages = [
            'fastapi', 'django', 'groq', 'langchain', 
            'pyodbc', 'pandas', 'plotly'
        ]
        
        missing = []
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing.append(package)
        
        if missing:
            print(f"❌ Missing packages: {', '.join(missing)}")
            print("Please run: pip install -r requirements.txt")
            return False
        
        print("✅ All requirements satisfied")
        return True
    
    def check_environment(self):
        """Check environment configuration"""
        print("🔧 Checking environment...")
        
        env_file = Path(".env")
        if not env_file.exists():
            print("⚠️ .env file not found. Copying from .env.example...")
            example_file = Path(".env.example")
            if example_file.exists():
                import shutil
                shutil.copy(example_file, env_file)
                print("📝 Please edit .env file with your configuration")
            else:
                print("❌ .env.example not found")
                return False
        
        # Load and check critical environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        critical_vars = ['GROQ_API_KEY', 'DB_SERVER', 'DB_NAME']
        missing_vars = []
        
        for var in critical_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"⚠️ Missing environment variables: {', '.join(missing_vars)}")
            print("Please configure these in your .env file")
        
        print("✅ Environment check completed")
        return True
    
    def setup_database(self):
        """Setup database if needed"""
        print("🗄️ Checking database setup...")
        
        try:
            # Test database connection
            sys.path.append(str(Path("backend")))
            from backend.utils.database_utils import DatabaseUtils
            
            if DatabaseUtils.test_connection():
                print("✅ Database connection successful")
                return True
            else:
                print("❌ Database connection failed")
                print("Run 'python setup_db.py' to setup the database")
                return False
                
        except Exception as e:
            print(f"⚠️ Database check failed: {e}")
            print("You may need to run 'python setup_db.py'")
            return False
    
    def start_backend(self):
        """Start backend service"""
        print("🚀 Starting Backend API...")
        
        try:
            os.chdir("backend")
            self.backend_process = subprocess.Popen([
                sys.executable, "-m", "uvicorn", 
                "main:app", 
                "--reload", 
                "--host", "127.0.0.1", 
                "--port", "8000"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # Wait for backend to start
            for i in range(30):  # Wait up to 30 seconds
                try:
                    response = requests.get(f"{self.backend_url}/health", timeout=2)
                    if response.status_code == 200:
                        print("✅ Backend API started successfully")
                        return True
                except:
                    pass
                time.sleep(1)
                print(f"⏳ Waiting for backend... ({i+1}/30)")
            
            print("❌ Backend failed to start within 30 seconds")
            return False
            
        except Exception as e:
            print(f"❌ Failed to start backend: {e}")
            return False
        finally:
            os.chdir("..")
    
    def start_frontend(self):
        """Start frontend service"""
        print("🌐 Starting Frontend...")
        
        try:
            os.chdir("frontend")
            
            # Run migrations
            print("📦 Running Django migrations...")
            migration_result = subprocess.run([
                sys.executable, "manage.py", "migrate"
            ], capture_output=True, text=True)
            
            if migration_result.returncode == 0:
                print("✅ Migrations completed")
            else:
                print(f"⚠️ Migration warnings: {migration_result.stderr}")
            
            # Start Django server
            self.frontend_process = subprocess.Popen([
                sys.executable, "manage.py", "runserver", 
                "127.0.0.1:8080"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # Wait for frontend to start
            for i in range(20):  # Wait up to 20 seconds
                try:
                    response = requests.get(self.frontend_url, timeout=2)
                    if response.status_code == 200:
                        print("✅ Frontend started successfully")
                        return True
                except:
                    pass
                time.sleep(1)
                print(f"⏳ Waiting for frontend... ({i+1}/20)")
            
            print("❌ Frontend failed to start within 20 seconds")
            return False
            
        except Exception as e:
            print(f"❌ Failed to start frontend: {e}")
            return False
        finally:
            os.chdir("..")
    
    def monitor_system(self):
        """Monitor system health"""
        print("📊 Starting system monitoring...")
        
        while self.running:
            try:
                time.sleep(30)  # Check every 30 seconds
                
                # Check backend health
                try:
                    response = requests.get(f"{self.backend_url}/health", timeout=5)
                    backend_status = "✅" if response.status_code == 200 else "❌"
                except:
                    backend_status = "❌"
                
                # Check frontend health
                try:
                    response = requests.get(self.frontend_url, timeout=5)
                    frontend_status = "✅" if response.status_code == 200 else "❌"
                except:
                    frontend_status = "❌"
                
                # Get performance metrics
                try:
                    response = requests.get(f"{self.backend_url}/performance/metrics", timeout=5)
                    if response.status_code == 200:
                        metrics = response.json()["metrics"]
                        print(f"📈 System Status - Backend: {backend_status} Frontend: {frontend_status} "
                              f"Queries: {metrics['total_queries']} "
                              f"Avg Response: {metrics['avg_response_time']:.2f}s "
                              f"Success Rate: {metrics['success_rate']:.1%}")
                    else:
                        print(f"📊 System Status - Backend: {backend_status} Frontend: {frontend_status}")
                except:
                    print(f"📊 System Status - Backend: {backend_status} Frontend: {frontend_status}")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"⚠️ Monitoring error: {e}")
    
    def cleanup(self):
        """Cleanup processes"""
        print("\n🛑 Shutting down system...")
        self.running = False
        
        if self.backend_process:
            self.backend_process.terminate()
            print("🔴 Backend stopped")
        
        if self.frontend_process:
            self.frontend_process.terminate()
            print("🔴 Frontend stopped")
        
        print("👋 System shutdown complete")
    
    def run(self):
        """Main run method"""
        print("=" * 70)
        print("🤖 نظام الوكيل الذكي للاستعلامات المحاسبية")
        print("   Intelligent Accounting Agent System")
        print("=" * 70)
        print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            # Pre-flight checks
            if not self.check_requirements():
                return False
            
            if not self.check_environment():
                return False
            
            if not self.setup_database():
                print("⚠️ Database issues detected. System may not work properly.")
            
            # Create logs directory
            Path("logs").mkdir(exist_ok=True)
            Path("frontend/logs").mkdir(exist_ok=True)
            
            # Start services
            if not self.start_backend():
                return False
            
            if not self.start_frontend():
                return False
            
            # Display access information
            print("\n" + "=" * 50)
            print("🎉 System started successfully!")
            print(f"🌐 Frontend:     {self.frontend_url}")
            print(f"🔧 Backend API:  {self.backend_url}")
            print(f"📚 API Docs:     {self.backend_url}/docs")
            print(f"📊 Metrics:      {self.backend_url}/performance/metrics")
            print("=" * 50)
            print("Press Ctrl+C to stop the system")
            
            # Start monitoring
            monitor_thread = threading.Thread(target=self.monitor_system, daemon=True)
            monitor_thread.start()
            
            # Wait for interrupt
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass
            
            return True
            
        except Exception as e:
            print(f"❌ System startup failed: {e}")
            return False
        finally:
            self.cleanup()

def main():
    """Main entry point"""
    launcher = SystemLauncher()
    
    # Handle signals
    def signal_handler(signum, frame):
        launcher.cleanup()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    success = launcher.run()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
